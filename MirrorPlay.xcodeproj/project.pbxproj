// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		E244D2892E31B757006F7A69 /* ReplayKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E244D2882E31B757006F7A69 /* ReplayKit.framework */; };
		E244D2932E31B758006F7A69 /* ReplayKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E244D2882E31B757006F7A69 /* ReplayKit.framework */; };
		E244D2952E31B758006F7A69 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E244D2942E31B758006F7A69 /* UIKit.framework */; };
		E244D29C2E31B758006F7A69 /* uploadSetupUI.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = E244D2922E31B758006F7A69 /* uploadSetupUI.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		E244D29F2E31B758006F7A69 /* upload.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = E244D2862E31B757006F7A69 /* upload.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		E244D29A2E31B758006F7A69 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E244D2622E31B6FC006F7A69 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E244D2912E31B758006F7A69;
			remoteInfo = uploadSetupUI;
		};
		E244D29D2E31B758006F7A69 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E244D2622E31B6FC006F7A69 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E244D2852E31B757006F7A69;
			remoteInfo = upload;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		E244D2A82E31B758006F7A69 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				E244D29C2E31B758006F7A69 /* uploadSetupUI.appex in Embed Foundation Extensions */,
				E244D29F2E31B758006F7A69 /* upload.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		E244D26A2E31B6FC006F7A69 /* MirrorPlay.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MirrorPlay.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E244D2862E31B757006F7A69 /* upload.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = upload.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		E244D2882E31B757006F7A69 /* ReplayKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ReplayKit.framework; path = System/Library/Frameworks/ReplayKit.framework; sourceTree = SDKROOT; };
		E244D2922E31B758006F7A69 /* uploadSetupUI.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = uploadSetupUI.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		E244D2942E31B758006F7A69 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		E244D27C2E31B6FF006F7A69 /* Exceptions for "MirrorPlay" folder in "MirrorPlay" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = E244D2692E31B6FC006F7A69 /* MirrorPlay */;
		};
		E244D2A32E31B758006F7A69 /* Exceptions for "uploadSetupUI" folder in "uploadSetupUI" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = E244D2912E31B758006F7A69 /* uploadSetupUI */;
		};
		E244D2A72E31B758006F7A69 /* Exceptions for "upload" folder in "upload" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = E244D2852E31B757006F7A69 /* upload */;
		};
		E244D2B82E31BE88006F7A69 /* Exceptions for "MirrorPlay" folder in "upload" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				AudioDecoder.swift,
				AudioEncoder.swift,
				BluetoothManager.swift,
				NetworkManager.swift,
				SharedDataManager.swift,
				VideoDecoder.swift,
				VideoEncoder.swift,
			);
			target = E244D2852E31B757006F7A69 /* upload */;
		};
		E26D21402E375DE40009BCC6 /* Exceptions for "MirrorPlay" folder in "uploadSetupUI" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				SharedDataManager.swift,
			);
			target = E244D2912E31B758006F7A69 /* uploadSetupUI */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		E244D26C2E31B6FC006F7A69 /* MirrorPlay */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				E244D27C2E31B6FF006F7A69 /* Exceptions for "MirrorPlay" folder in "MirrorPlay" target */,
				E244D2B82E31BE88006F7A69 /* Exceptions for "MirrorPlay" folder in "upload" target */,
				E26D21402E375DE40009BCC6 /* Exceptions for "MirrorPlay" folder in "uploadSetupUI" target */,
			);
			path = MirrorPlay;
			sourceTree = "<group>";
		};
		E244D28A2E31B757006F7A69 /* upload */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				E244D2A72E31B758006F7A69 /* Exceptions for "upload" folder in "upload" target */,
			);
			path = upload;
			sourceTree = "<group>";
		};
		E244D2962E31B758006F7A69 /* uploadSetupUI */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				E244D2A32E31B758006F7A69 /* Exceptions for "uploadSetupUI" folder in "uploadSetupUI" target */,
			);
			path = uploadSetupUI;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		E244D2672E31B6FC006F7A69 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E244D2832E31B757006F7A69 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E244D2892E31B757006F7A69 /* ReplayKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E244D28F2E31B758006F7A69 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E244D2932E31B758006F7A69 /* ReplayKit.framework in Frameworks */,
				E244D2952E31B758006F7A69 /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		E244D2612E31B6FC006F7A69 = {
			isa = PBXGroup;
			children = (
				E244D26C2E31B6FC006F7A69 /* MirrorPlay */,
				E244D28A2E31B757006F7A69 /* upload */,
				E244D2962E31B758006F7A69 /* uploadSetupUI */,
				E244D2872E31B757006F7A69 /* Frameworks */,
				E244D26B2E31B6FC006F7A69 /* Products */,
			);
			sourceTree = "<group>";
		};
		E244D26B2E31B6FC006F7A69 /* Products */ = {
			isa = PBXGroup;
			children = (
				E244D26A2E31B6FC006F7A69 /* MirrorPlay.app */,
				E244D2862E31B757006F7A69 /* upload.appex */,
				E244D2922E31B758006F7A69 /* uploadSetupUI.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E244D2872E31B757006F7A69 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E244D2882E31B757006F7A69 /* ReplayKit.framework */,
				E244D2942E31B758006F7A69 /* UIKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		E244D2692E31B6FC006F7A69 /* MirrorPlay */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E244D27D2E31B6FF006F7A69 /* Build configuration list for PBXNativeTarget "MirrorPlay" */;
			buildPhases = (
				E244D2662E31B6FC006F7A69 /* Sources */,
				E244D2672E31B6FC006F7A69 /* Frameworks */,
				E244D2682E31B6FC006F7A69 /* Resources */,
				E244D2A82E31B758006F7A69 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				E244D29B2E31B758006F7A69 /* PBXTargetDependency */,
				E244D29E2E31B758006F7A69 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E244D26C2E31B6FC006F7A69 /* MirrorPlay */,
			);
			name = MirrorPlay;
			packageProductDependencies = (
			);
			productName = MirrorPlay;
			productReference = E244D26A2E31B6FC006F7A69 /* MirrorPlay.app */;
			productType = "com.apple.product-type.application";
		};
		E244D2852E31B757006F7A69 /* upload */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E244D2A42E31B758006F7A69 /* Build configuration list for PBXNativeTarget "upload" */;
			buildPhases = (
				E244D2822E31B757006F7A69 /* Sources */,
				E244D2832E31B757006F7A69 /* Frameworks */,
				E244D2842E31B757006F7A69 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				E244D28A2E31B757006F7A69 /* upload */,
			);
			name = upload;
			packageProductDependencies = (
			);
			productName = upload;
			productReference = E244D2862E31B757006F7A69 /* upload.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		E244D2912E31B758006F7A69 /* uploadSetupUI */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E244D2A02E31B758006F7A69 /* Build configuration list for PBXNativeTarget "uploadSetupUI" */;
			buildPhases = (
				E244D28E2E31B758006F7A69 /* Sources */,
				E244D28F2E31B758006F7A69 /* Frameworks */,
				E244D2902E31B758006F7A69 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				E244D2962E31B758006F7A69 /* uploadSetupUI */,
			);
			name = uploadSetupUI;
			packageProductDependencies = (
			);
			productName = uploadSetupUI;
			productReference = E244D2922E31B758006F7A69 /* uploadSetupUI.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E244D2622E31B6FC006F7A69 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					E244D2692E31B6FC006F7A69 = {
						CreatedOnToolsVersion = 16.4;
					};
					E244D2852E31B757006F7A69 = {
						CreatedOnToolsVersion = 16.4;
					};
					E244D2912E31B758006F7A69 = {
						CreatedOnToolsVersion = 16.4;
					};
				};
			};
			buildConfigurationList = E244D2652E31B6FC006F7A69 /* Build configuration list for PBXProject "MirrorPlay" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = E244D2612E31B6FC006F7A69;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = E244D26B2E31B6FC006F7A69 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				E244D2692E31B6FC006F7A69 /* MirrorPlay */,
				E244D2852E31B757006F7A69 /* upload */,
				E244D2912E31B758006F7A69 /* uploadSetupUI */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		E244D2682E31B6FC006F7A69 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E244D2842E31B757006F7A69 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E244D2902E31B758006F7A69 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E244D2662E31B6FC006F7A69 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E244D2822E31B757006F7A69 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E244D28E2E31B758006F7A69 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		E244D29B2E31B758006F7A69 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E244D2912E31B758006F7A69 /* uploadSetupUI */;
			targetProxy = E244D29A2E31B758006F7A69 /* PBXContainerItemProxy */;
		};
		E244D29E2E31B758006F7A69 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E244D2852E31B757006F7A69 /* upload */;
			targetProxy = E244D29D2E31B758006F7A69 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		E244D27E2E31B6FF006F7A69 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MirrorPlay/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "MirrorPlay-1";
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "MirrorPlay需要使用蓝牙来连接和控制投屏设备";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "MirrorPlay需要使用蓝牙来连接和控制投屏设备";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "MirrorPlay需要访问本地网络来传输投屏数据";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "MirrorPlay需要访问麦克风来录制音频";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.MirrorPlay.play;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E244D27F2E31B6FF006F7A69 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MirrorPlay/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "MirrorPlay-1";
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "MirrorPlay需要使用蓝牙来连接和控制投屏设备";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "MirrorPlay需要使用蓝牙来连接和控制投屏设备";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "MirrorPlay需要访问本地网络来传输投屏数据";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "MirrorPlay需要访问麦克风来录制音频";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.MirrorPlay.play;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		E244D2802E31B6FF006F7A69 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		E244D2812E31B6FF006F7A69 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		E244D2A12E31B758006F7A69 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = uploadSetupUI/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "MirrorPlay设置";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.MirrorPlay.play.uploadSetupUI;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E244D2A22E31B758006F7A69 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = uploadSetupUI/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "MirrorPlay设置";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.MirrorPlay.play.uploadSetupUI;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		E244D2A52E31B758006F7A69 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = upload/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "MirrorPlay录屏";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "录屏扩展需要访问本地网络来传输投屏数据";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.MirrorPlay.play.upload;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E244D2A62E31B758006F7A69 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 6SU7KUCNM4;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = upload/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "MirrorPlay录屏";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "录屏扩展需要访问本地网络来传输投屏数据";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.test.MirrorPlay.play.upload;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		E244D2652E31B6FC006F7A69 /* Build configuration list for PBXProject "MirrorPlay" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E244D2802E31B6FF006F7A69 /* Debug */,
				E244D2812E31B6FF006F7A69 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E244D27D2E31B6FF006F7A69 /* Build configuration list for PBXNativeTarget "MirrorPlay" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E244D27E2E31B6FF006F7A69 /* Debug */,
				E244D27F2E31B6FF006F7A69 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E244D2A02E31B758006F7A69 /* Build configuration list for PBXNativeTarget "uploadSetupUI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E244D2A12E31B758006F7A69 /* Debug */,
				E244D2A22E31B758006F7A69 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E244D2A42E31B758006F7A69 /* Build configuration list for PBXNativeTarget "upload" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E244D2A52E31B758006F7A69 /* Debug */,
				E244D2A62E31B758006F7A69 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = E244D2622E31B6FC006F7A69 /* Project object */;
}
