<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "690AB00B-196D-481D-95AE-458709E41F77"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2560A66B-9015-43CF-B67A-EA0218553262"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MirrorPlay/NetworkManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "302"
            endingLineNumber = "302"
            landmarkName = "receiveCompletePacket(connection:clientId:headerData:dataSize:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C61D83F5-3E02-4AFC-A591-F7934726E6A7"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "upload/SampleHandler.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "127"
            endingLineNumber = "127"
            landmarkName = "SampleHandler"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2CA3C327-135E-410A-9361-A1C898736C58"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "upload/SampleHandler.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "42"
            endingLineNumber = "42"
            landmarkName = "SampleHandler"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "55D00899-422F-4216-9BB0-FA472AE242ED"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "upload/SampleHandler.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "18"
            endingLineNumber = "18"
            landmarkName = "debugLoaded"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
