<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "690AB00B-196D-481D-95AE-458709E41F77"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2560A66B-9015-43CF-B67A-EA0218553262"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MirrorPlay/NetworkManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "302"
            endingLineNumber = "302"
            landmarkName = "receiveCompletePacket(connection:clientId:headerData:dataSize:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0AA3B3F2-CE88-49BD-B27E-95F07E06B2DB"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "upload/SampleHandler.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "32"
            endingLineNumber = "32"
            landmarkName = "broadcastStarted(withSetupInfo:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C61D83F5-3E02-4AFC-A591-F7934726E6A7"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "upload/SampleHandler.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "127"
            endingLineNumber = "127"
            landmarkName = "handleVideoSampleBuffer(_:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
