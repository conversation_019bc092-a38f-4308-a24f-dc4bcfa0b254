<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "690AB00B-196D-481D-95AE-458709E41F77"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2560A66B-9015-43CF-B67A-EA0218553262"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MirrorPlay/NetworkManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "302"
            endingLineNumber = "302"
            landmarkName = "connectToServer(host:port:completion:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
