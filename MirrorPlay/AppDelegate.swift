//
//  AppDelegate.swift
//  MirrorPlay
//
//  Created by cf110 on 2025/7/24.
//

import UIKit
import Network

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.
        
        print("🚀 MirrorPlay应用启动")
        
        // 申请网络权限
        requestNetworkPermissions()
        
        return true
    }
    
    // 申请网络权限
    private func requestNetworkPermissions() {
        print("🌐 申请网络权限...")
        
        // 创建网络监听器来触发权限申请
        let monitor = NWPathMonitor()
        monitor.pathUpdateHandler = { path in
            DispatchQueue.main.async {
                if path.status == .satisfied {
                    print("✅ 网络权限已获取")
                } else {
                    print("⚠️ 网络权限未获取")
                }
            }
        }
        monitor.start(queue: DispatchQueue.global())
        
        // 延迟停止监听器
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            monitor.cancel()
        }
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene session is being created.
        // Use this method to select a configuration to create the new scene with.
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }
}

