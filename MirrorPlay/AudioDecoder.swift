//
//  AudioDecoder.swift
//  MirrorPlay
//
//  Created by cf110 on 2025/7/24.
//

import Foundation
import AudioToolbox
import CoreMedia
import AVFoundation

protocol AudioDecoderDelegate: AnyObject {
    func audioDecoder(_ decoder: AudioDecoder, didDecodeFrame data: Data, timestamp: UInt64)
    func audioDecoder(_ decoder: AudioDecoder, didEncounterError error: Error)
}

class AudioDecoder {
    weak var delegate: AudioDecoderDelegate?
    
    private var audioConverter: AudioConverterRef?
    private let decodingQueue = DispatchQueue(label: "AudioDecoder", qos: .userInitiated)
    
    // 音频格式参数
    private let sampleRate: Float64
    private let channels: UInt32
    
    // 输入和输出格式
    private var inputFormat: AudioStreamBasicDescription
    private var outputFormat: AudioStreamBasicDescription
    
    init(sampleRate: Float64 = 44100, channels: UInt32 = 2) {
        self.sampleRate = sampleRate
        self.channels = channels
        
        // 设置输入格式（AAC）
        self.inputFormat = AudioStreamBasicDescription(
            mSampleRate: sampleRate,
            mFormatID: kAudioFormatMPEG4AAC,
            mFormatFlags: kMPEG4Object_AAC_LC,
            mBytesPerPacket: 0, // 可变长度
            mFramesPerPacket: 1024, // AAC帧大小
            mBytesPerFrame: 0,
            mChannelsPerFrame: channels,
            mBitsPerChannel: 0,
            mReserved: 0
        )
        
        // 设置输出格式（PCM）
        self.outputFormat = AudioStreamBasicDescription(
            mSampleRate: sampleRate,
            mFormatID: kAudioFormatLinearPCM,
            mFormatFlags: kAudioFormatFlagIsFloat | kAudioFormatFlagIsPacked,
            mBytesPerPacket: UInt32(MemoryLayout<Float32>.size) * channels,
            mFramesPerPacket: 1,
            mBytesPerFrame: UInt32(MemoryLayout<Float32>.size) * channels,
            mChannelsPerFrame: channels,
            mBitsPerChannel: 32,
            mReserved: 0
        )
    }
    
    deinit {
        stopDecoding()
    }
    
    func startDecoding() throws {
        guard audioConverter == nil else { return }
        
        // 创建音频转换器
        let status = AudioConverterNew(&inputFormat, &outputFormat, &audioConverter)
        guard status == noErr, audioConverter != nil else {
            throw AudioDecoderError.converterCreationFailed(status)
        }
        
        print("Audio decoder started: \(sampleRate) Hz, \(channels) channels")
    }
    
    func stopDecoding() {
        if let converter = audioConverter {
            AudioConverterDispose(converter)
            audioConverter = nil
        }
        print("Audio decoder stopped")
    }
    
    func decodeAudioFrame(_ data: Data, timestamp: UInt64) {
        guard let converter = audioConverter else {
            delegate?.audioDecoder(self, didEncounterError: AudioDecoderError.converterNotReady)
            return
        }
        
        decodingQueue.async { [weak self] in
            self?.processAudioFrame(data, timestamp: timestamp, converter: converter)
        }
    }
    
    private func processAudioFrame(_ data: Data, timestamp: UInt64, converter: AudioConverterRef) {
        // 准备输入数据
        var inputData = data
        var inputDataPtr = inputData.withUnsafeMutableBytes { $0.bindMemory(to: UInt8.self).baseAddress }
        
        // 准备输出缓冲区
        let maxOutputSize = 1024 * 4 * Int(channels) // 足够大的输出缓冲区
        var outputData = Data(count: maxOutputSize)
        
        var outputBufferList = outputData.withUnsafeMutableBytes { bytes in
            AudioBufferList(
                mNumberBuffers: 1,
                mBuffers: AudioBuffer(
                    mNumberChannels: channels,
                    mDataByteSize: UInt32(maxOutputSize),
                    mData: bytes.bindMemory(to: UInt8.self).baseAddress
                )
            )
        }
        
        // 转换回调
        let inputProc: AudioConverterComplexInputDataProc = { (
            inAudioConverter,
            ioNumberDataPackets,
            ioData,
            outDataPacketDescription,
            inUserData
        ) in
            guard let userData = inUserData else { return kAudioConverterErr_InvalidInputSize }
            
            let context = Unmanaged<AudioDecoderContext>.fromOpaque(userData).takeUnretainedValue()
            
            if context.hasData {
                ioData?.pointee.mNumberBuffers = 1
                ioData?.pointee.mBuffers.mNumberChannels = context.channels
                ioData?.pointee.mBuffers.mDataByteSize = UInt32(context.inputData.count)
                ioData?.pointee.mBuffers.mData = context.inputDataPtr
                
                context.hasData = false
                return noErr
            } else {
                ioNumberDataPackets?.pointee = 0
                return kAudioConverterErr_InvalidInputSize
            }
        }
        
        // 创建上下文
        let context = AudioDecoderContext(
            inputData: inputData,
            inputDataPtr: inputDataPtr,
            channels: channels,
            hasData: true
        )
        
        let contextPtr = Unmanaged.passUnretained(context).toOpaque()
        
        // 执行转换
        var outputPacketCount: UInt32 = 1024 // 期望输出的帧数
        let status = AudioConverterFillComplexBuffer(
            converter,
            inputProc,
            contextPtr,
            &outputPacketCount,
            &outputBufferList,
            nil
        )
        
        guard status == noErr && outputPacketCount > 0 else {
            if status != noErr {
                delegate?.audioDecoder(self, didEncounterError: AudioDecoderError.decodingFailed(status))
            }
            return
        }
        
        // 提取解码后的数据
        let decodedSize = Int(outputBufferList.mBuffers.mDataByteSize)
        let decodedData = outputData.prefix(decodedSize)
        
        delegate?.audioDecoder(self, didDecodeFrame: Data(decodedData), timestamp: timestamp)
    }
}

// MARK: - 辅助类

private class AudioDecoderContext {
    let inputData: Data
    let inputDataPtr: UnsafeMutablePointer<UInt8>?
    let channels: UInt32
    var hasData: Bool
    
    init(inputData: Data, inputDataPtr: UnsafeMutablePointer<UInt8>?, channels: UInt32, hasData: Bool) {
        self.inputData = inputData
        self.inputDataPtr = inputDataPtr
        self.channels = channels
        self.hasData = hasData
    }
}

// MARK: - 错误定义

enum AudioDecoderError: Error, LocalizedError {
    case converterCreationFailed(OSStatus)
    case converterNotReady
    case decodingFailed(OSStatus)
    
    var errorDescription: String? {
        switch self {
        case .converterCreationFailed(let status):
            return "Failed to create audio converter: \(status)"
        case .converterNotReady:
            return "Audio converter not ready"
        case .decodingFailed(let status):
            return "Audio decoding failed: \(status)"
        }
    }
}

// MARK: - 音频播放器

class AudioPlayer {
    private var audioEngine: AVAudioEngine?
    private var playerNode: AVAudioPlayerNode?
    private var audioFormat: AVAudioFormat?
    
    init() {
        setupAudioEngine()
    }
    
    deinit {
        stop()
    }
    
    private func setupAudioEngine() {
        audioEngine = AVAudioEngine()
        playerNode = AVAudioPlayerNode()
        
        guard let engine = audioEngine, let player = playerNode else { return }
        
        engine.attach(player)
        
        // 设置音频格式
        audioFormat = AVAudioFormat(standardFormatWithSampleRate: 44100, channels: 2)
        
        if let format = audioFormat {
            engine.connect(player, to: engine.mainMixerNode, format: format)
        }
    }
    
    func start() throws {
        guard let engine = audioEngine, let player = playerNode else {
            throw AudioPlayerError.engineNotReady
        }
        
        try engine.start()
        player.play()
        
        print("Audio player started")
    }
    
    func stop() {
        playerNode?.stop()
        audioEngine?.stop()
        print("Audio player stopped")
    }
    
    func playAudioData(_ data: Data) {
        guard let player = playerNode,
              let format = audioFormat else { return }
        
        // 将PCM数据转换为AVAudioPCMBuffer
        let frameCount = data.count / (Int(format.channelCount) * MemoryLayout<Float32>.size)
        
        guard let buffer = AVAudioPCMBuffer(pcmFormat: format, frameCapacity: AVAudioFrameCount(frameCount)) else {
            return
        }
        
        buffer.frameLength = AVAudioFrameCount(frameCount)
        
        // 复制音频数据
        data.withUnsafeBytes { bytes in
            let floatPtr = bytes.bindMemory(to: Float32.self)
            
            for channel in 0..<Int(format.channelCount) {
                let channelData = buffer.floatChannelData![channel]
                for frame in 0..<frameCount {
                    channelData[frame] = floatPtr[frame * Int(format.channelCount) + channel]
                }
            }
        }
        
        // 播放音频缓冲区
        player.scheduleBuffer(buffer, at: nil, options: [], completionHandler: nil)
    }
}

// MARK: - 音频播放器错误

enum AudioPlayerError: Error, LocalizedError {
    case engineNotReady
    
    var errorDescription: String? {
        switch self {
        case .engineNotReady:
            return "Audio engine not ready"
        }
    }
}
