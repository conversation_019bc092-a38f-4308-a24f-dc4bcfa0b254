//
//  AudioEncoder.swift
//  MirrorPlay
//
//  Created by cf110 on 2025/7/24.
//

import Foundation
import AudioToolbox
import CoreMedia
import AVFoundation

protocol AudioEncoderDelegate: AnyObject {
    func audioEncoder(_ encoder: AudioEncoder, didEncodeFrame data: Data, timestamp: UInt64)
    func audioEncoder(_ encoder: AudioEncoder, didEncounterError error: Error)
}

class AudioEncoder {
    weak var delegate: AudioEncoderDelegate?
    
    private var audioConverter: AudioConverterRef?
    private let encodingQueue = DispatchQueue(label: "AudioEncoder", qos: .userInitiated)
    
    // 音频格式参数
    private let sampleRate: Float64
    private let channels: UInt32
    private let bitrate: UInt32
    
    // 输入和输出格式
    private var inputFormat: AudioStreamBasicDescription
    private var outputFormat: AudioStreamBasicDescription
    
    // 缓冲区
    private var inputBuffer: Data = Data()
    private let frameSize: Int = 1024 // AAC帧大小
    
    init(sampleRate: Float64 = 44100, channels: UInt32 = 2, bitrate: UInt32 = 128000) {
        self.sampleRate = sampleRate
        self.channels = channels
        self.bitrate = bitrate
        
        // 设置输入格式（PCM）
        self.inputFormat = AudioStreamBasicDescription(
            mSampleRate: sampleRate,
            mFormatID: kAudioFormatLinearPCM,
            mFormatFlags: kAudioFormatFlagIsFloat | kAudioFormatFlagIsPacked,
            mBytesPerPacket: UInt32(MemoryLayout<Float32>.size) * channels,
            mFramesPerPacket: 1,
            mBytesPerFrame: UInt32(MemoryLayout<Float32>.size) * channels,
            mChannelsPerFrame: channels,
            mBitsPerChannel: 32,
            mReserved: 0
        )
        
        // 设置输出格式（AAC）
        self.outputFormat = AudioStreamBasicDescription(
            mSampleRate: sampleRate,
            mFormatID: kAudioFormatMPEG4AAC,
            mFormatFlags: kAudioFormatMPEG4AAC_LD,
            mBytesPerPacket: 0, // 可变长度
            mFramesPerPacket: 1024, // AAC帧大小
            mBytesPerFrame: 0,
            mChannelsPerFrame: channels,
            mBitsPerChannel: 0,
            mReserved: 0
        )
    }
    
    deinit {
        stopEncoding()
    }
    
    func startEncoding() throws {
        guard audioConverter == nil else { return }
        
        // 创建音频转换器
        let status = AudioConverterNew(&inputFormat, &outputFormat, &audioConverter)
        guard status == noErr, let converter = audioConverter else {
            throw AudioEncoderError.converterCreationFailed(status)
        }
        
        // 配置转换器
        try configureConverter(converter)
        
        print("Audio encoder started: \(sampleRate) Hz, \(channels) channels, \(bitrate) bps")
    }
    
    func stopEncoding() {
        if let converter = audioConverter {
            AudioConverterDispose(converter)
            audioConverter = nil
        }
        inputBuffer.removeAll()
        print("Audio encoder stopped")
    }
    
    private func configureConverter(_ converter: AudioConverterRef) throws {
        // 设置码率
        var bitrateValue = bitrate
        let bitrateSize = UInt32(MemoryLayout<UInt32>.size)
        let status = AudioConverterSetProperty(
            converter,
            kAudioConverterEncodeBitRate,
            bitrateSize,
            &bitrateValue
        )
        
        guard status == noErr else {
            throw AudioEncoderError.configurationFailed("BitRate", status)
        }
    }
    
    func encodeAudioBuffer(_ sampleBuffer: CMSampleBuffer) {
        guard let converter = audioConverter else {
            delegate?.audioEncoder(self, didEncounterError: AudioEncoderError.converterNotReady)
            return
        }
        
        encodingQueue.async { [weak self] in
            self?.processAudioBuffer(sampleBuffer, converter: converter)
        }
    }
    
    private func processAudioBuffer(_ sampleBuffer: CMSampleBuffer, converter: AudioConverterRef) {
        guard let blockBuffer = CMSampleBufferGetDataBuffer(sampleBuffer) else {
            delegate?.audioEncoder(self, didEncounterError: AudioEncoderError.invalidSampleBuffer)
            return
        }
        
        // 获取音频数据
        let dataLength = CMBlockBufferGetDataLength(blockBuffer)
        var audioData = Data(count: dataLength)
        
        let copyStatus = audioData.withUnsafeMutableBytes { bytes in
            CMBlockBufferCopyDataBytes(
                blockBuffer,
                atOffset: 0,
                dataLength: dataLength,
                destination: bytes.bindMemory(to: UInt8.self).baseAddress!
            )
        }
        
        guard copyStatus == noErr else {
            delegate?.audioEncoder(self, didEncounterError: AudioEncoderError.dataCopyFailed(copyStatus))
            return
        }
        
        // 添加到输入缓冲区
        inputBuffer.append(audioData)
        
        // 获取时间戳
        let presentationTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
        let timestamp = UInt64(CMTimeGetSeconds(presentationTime) * 1000)
        
        // 处理完整的帧
        processCompleteFrames(converter: converter, timestamp: timestamp)
    }
    
    private func processCompleteFrames(converter: AudioConverterRef, timestamp: UInt64) {
        let bytesPerFrame = Int(inputFormat.mBytesPerFrame)
        let samplesPerFrame = frameSize
        let bytesNeeded = samplesPerFrame * bytesPerFrame
        
        while inputBuffer.count >= bytesNeeded {
            let frameData = inputBuffer.prefix(bytesNeeded)
            inputBuffer.removeFirst(bytesNeeded)
            
            encodeFrame(Data(frameData), converter: converter, timestamp: timestamp)
        }
    }
    
    private func encodeFrame(_ frameData: Data, converter: AudioConverterRef, timestamp: UInt64) {
        // 准备输入数据
        var inputData = frameData
        var inputDataPtr = inputData.withUnsafeMutableBytes { $0.bindMemory(to: UInt8.self).baseAddress }
        
        // 创建输入音频缓冲区列表
        var inputBufferList = AudioBufferList(
            mNumberBuffers: 1,
            mBuffers: AudioBuffer(
                mNumberChannels: channels,
                mDataByteSize: UInt32(frameData.count),
                mData: inputDataPtr
            )
        )
        
        // 准备输出缓冲区
        let maxOutputSize = 1024 * 8 // 足够大的输出缓冲区
        var outputData = Data(count: maxOutputSize)
        
        var outputBufferList = outputData.withUnsafeMutableBytes { bytes in
            AudioBufferList(
                mNumberBuffers: 1,
                mBuffers: AudioBuffer(
                    mNumberChannels: channels,
                    mDataByteSize: UInt32(maxOutputSize),
                    mData: bytes.bindMemory(to: UInt8.self).baseAddress
                )
            )
        }
        
        // 转换回调
        let inputProc: AudioConverterComplexInputDataProc = { (
            inAudioConverter,
            ioNumberDataPackets,
            ioData,
            outDataPacketDescription,
            inUserData
        ) in
            guard let userData = inUserData else { return kAudioConverterErr_InvalidInputSize }
            
            let context = Unmanaged<AudioEncoderContext>.fromOpaque(userData).takeUnretainedValue()
            
            if context.hasData {
                ioData.pointee.mNumberBuffers = 1
                ioData.pointee.mBuffers.mNumberChannels = context.channels
                ioData.pointee.mBuffers.mDataByteSize = UInt32(context.inputData.count)
                ioData.pointee.mBuffers.mData = context.inputDataPtr
                
                context.hasData = false
                return noErr
            } else {
                ioNumberDataPackets.pointee = 0
                return kAudioConverterErr_InvalidInputSize
            }
        }
        
        // 创建上下文
        let context = AudioEncoderContext(
            inputData: inputData,
            inputDataPtr: inputDataPtr,
            channels: channels,
            hasData: true
        )
        
        let contextPtr = Unmanaged.passUnretained(context).toOpaque()
        
        // 执行转换
        var outputPacketCount: UInt32 = 1
        let status = AudioConverterFillComplexBuffer(
            converter,
            inputProc,
            contextPtr,
            &outputPacketCount,
            &outputBufferList,
            nil
        )
        
        guard status == noErr && outputPacketCount > 0 else {
            if status != noErr {
                delegate?.audioEncoder(self, didEncounterError: AudioEncoderError.encodingFailed(status))
            }
            return
        }
        
        // 提取编码后的数据
        let encodedSize = Int(outputBufferList.mBuffers.mDataByteSize)
        let encodedData = outputData.prefix(encodedSize)
        
        delegate?.audioEncoder(self, didEncodeFrame: Data(encodedData), timestamp: timestamp)
    }
}

// MARK: - 辅助类

private class AudioEncoderContext {
    let inputData: Data
    let inputDataPtr: UnsafeMutableRawPointer?
    let channels: UInt32
    var hasData: Bool
    
    init(inputData: Data, inputDataPtr: UnsafeMutableRawPointer?, channels: UInt32, hasData: Bool) {
        self.inputData = inputData
        self.inputDataPtr = inputDataPtr
        self.channels = channels
        self.hasData = hasData
    }
}

// MARK: - 错误定义

enum AudioEncoderError: Error, LocalizedError {
    case converterCreationFailed(OSStatus)
    case configurationFailed(String, OSStatus)
    case converterNotReady
    case invalidSampleBuffer
    case dataCopyFailed(OSStatus)
    case encodingFailed(OSStatus)
    
    var errorDescription: String? {
        switch self {
        case .converterCreationFailed(let status):
            return "Failed to create audio converter: \(status)"
        case .configurationFailed(let property, let status):
            return "Failed to configure \(property): \(status)"
        case .converterNotReady:
            return "Audio converter not ready"
        case .invalidSampleBuffer:
            return "Invalid sample buffer"
        case .dataCopyFailed(let status):
            return "Failed to copy audio data: \(status)"
        case .encodingFailed(let status):
            return "Audio encoding failed: \(status)"
        }
    }
}

// MARK: - 工具扩展

extension AudioEncoder {
    // 从CMSampleBuffer获取音频格式信息
    static func audioFormat(from sampleBuffer: CMSampleBuffer) -> AudioStreamBasicDescription? {
        guard let formatDescription = CMSampleBufferGetFormatDescription(sampleBuffer) else {
            return nil
        }
        
        return CMAudioFormatDescriptionGetStreamBasicDescription(formatDescription)?.pointee
    }
    
    // 获取推荐的编码参数
    static func recommendedSettings(for format: AudioStreamBasicDescription) -> (sampleRate: Float64, channels: UInt32, bitrate: UInt32) {
        let sampleRate = format.mSampleRate
        let channels = format.mChannelsPerFrame
        
        // 根据声道数计算推荐码率
        let bitrate: UInt32
        switch channels {
        case 1:
            bitrate = 64000  // 64 kbps for mono
        case 2:
            bitrate = 128000 // 128 kbps for stereo
        default:
            bitrate = UInt32(channels * 64000) // 64 kbps per channel
        }
        
        return (sampleRate, channels, bitrate)
    }
}
