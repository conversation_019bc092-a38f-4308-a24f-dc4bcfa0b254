//
//  BluetoothManager.swift
//  MirrorPlay
//
//  Created by cf110 on 2025/7/24.
//

import Foundation
import CoreBluetooth

// 蓝牙服务和特征UUID
struct BluetoothConstants {
    static let serviceUUID = CBUUID(string: "12345678-1234-1234-1234-123456789ABC")
    static let controlCharacteristicUUID = CBUUID(string: "12345678-1234-1234-1234-123456789ABD")
    static let statusCharacteristicUUID = CBUUID(string: "12345678-1234-1234-1234-123456789ABE")
}

// 控制命令类型
enum ControlCommand: UInt8 {
    case startMirroring = 0x01
    case stopMirroring = 0x02
    case pauseMirroring = 0x03
    case resumeMirroring = 0x04
    case requestStatus = 0x05
    case volumeUp = 0x06
    case volumeDown = 0x07
    case mute = 0x08
    case unmute = 0x09
}

// 状态信息
enum MirroringStatus: UInt8 {
    case idle = 0x00
    case connecting = 0x01
    case connected = 0x02
    case mirroring = 0x03
    case paused = 0x04
    case error = 0xFF
}

// 蓝牙管理器协议
protocol BluetoothManagerDelegate: AnyObject {
    func bluetoothManager(_ manager: BluetoothManager, didReceiveCommand command: ControlCommand)
    func bluetoothManager(_ manager: BluetoothManager, didUpdateStatus status: MirroringStatus)
    func bluetoothManager(_ manager: BluetoothManager, didConnectToDevice device: CBPeripheral)
    func bluetoothManager(_ manager: BluetoothManager, didDisconnectFromDevice device: CBPeripheral)
    func bluetoothManager(_ manager: BluetoothManager, didEncounterError error: Error)
}

class BluetoothManager: NSObject {
    weak var delegate: BluetoothManagerDelegate?
    
    // Central Manager (客户端模式)
    private var centralManager: CBCentralManager?
    private var connectedPeripheral: CBPeripheral?
    private var controlCharacteristic: CBCharacteristic?
    private var statusCharacteristic: CBCharacteristic?
    
    // Peripheral Manager (服务端模式)
    private var peripheralManager: CBPeripheralManager?
    private var mirrorService: CBMutableService?
    private var controlChar: CBMutableCharacteristic?
    private var statusChar: CBMutableCharacteristic?
    
    var isScanning = false
    var isAdvertising = false
    var currentStatus: MirroringStatus = .idle
    
    override init() {
        super.init()
        setupCentralManager()
        setupPeripheralManager()
    }
    
    // MARK: - 初始化
    
    private func setupCentralManager() {
        centralManager = CBCentralManager(delegate: self, queue: nil)
    }
    
    private func setupPeripheralManager() {
        peripheralManager = CBPeripheralManager(delegate: self, queue: nil)
    }
    
    // MARK: - 公共方法
    
    func startScanning() {
        guard let centralManager = centralManager, centralManager.state == .poweredOn else {
            print("Central manager not ready")
            return
        }
        
        if !isScanning {
            centralManager.scanForPeripherals(withServices: [BluetoothConstants.serviceUUID], options: nil)
            isScanning = true
            print("Started scanning for devices")
        }
    }
    
    func stopScanning() {
        centralManager?.stopScan()
        isScanning = false
        print("Stopped scanning")
    }
    
    func startAdvertising() {
        guard let peripheralManager = peripheralManager, peripheralManager.state == .poweredOn else {
            print("Peripheral manager not ready")
            return
        }
        
        if !isAdvertising {
            let advertisementData: [String: Any] = [
                CBAdvertisementDataServiceUUIDsKey: [BluetoothConstants.serviceUUID],
                CBAdvertisementDataLocalNameKey: "MirrorPlay"
            ]
            
            peripheralManager.startAdvertising(advertisementData)
            isAdvertising = true
            print("Started advertising")
        }
    }
    
    func stopAdvertising() {
        peripheralManager?.stopAdvertising()
        isAdvertising = false
        print("Stopped advertising")
    }
    
    func sendCommand(_ command: ControlCommand) {
        guard let peripheral = connectedPeripheral,
              let characteristic = controlCharacteristic else {
            print("No connected device or characteristic")
            return
        }
        
        let data = Data([command.rawValue])
        peripheral.writeValue(data, for: characteristic, type: .withResponse)
    }
    
    func updateStatus(_ status: MirroringStatus) {
        currentStatus = status
        
        guard let peripheralManager = peripheralManager,
              let characteristic = statusChar else {
            return
        }
        
        let data = Data([status.rawValue])
        peripheralManager.updateValue(data, for: characteristic, onSubscribedCentrals: nil)
        
        delegate?.bluetoothManager(self, didUpdateStatus: status)
    }
    
    func disconnect() {
        if let peripheral = connectedPeripheral {
            centralManager?.cancelPeripheralConnection(peripheral)
        }
    }
}

// MARK: - CBCentralManagerDelegate

extension BluetoothManager: CBCentralManagerDelegate {
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        switch central.state {
        case .poweredOn:
            print("Central Manager powered on")
        case .poweredOff:
            print("Central Manager powered off")
        case .resetting:
            print("Central Manager resetting")
        case .unauthorized:
            print("Central Manager unauthorized")
        case .unsupported:
            print("Central Manager unsupported")
        case .unknown:
            print("Central Manager unknown state")
        @unknown default:
            print("Central Manager unknown state")
        }
    }
    
    func centralManager(_ central: CBCentralManager, didDiscover peripheral: CBPeripheral, advertisementData: [String : Any], rssi RSSI: NSNumber) {
        print("Discovered peripheral: \(peripheral.name ?? "Unknown")")
        
        // 连接到发现的设备
        connectedPeripheral = peripheral
        peripheral.delegate = self
        central.connect(peripheral, options: nil)
        stopScanning()
    }
    
    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        print("Connected to peripheral: \(peripheral.name ?? "Unknown")")
        delegate?.bluetoothManager(self, didConnectToDevice: peripheral)
        
        // 发现服务
        peripheral.discoverServices([BluetoothConstants.serviceUUID])
    }
    
    func centralManager(_ central: CBCentralManager, didDisconnectPeripheral peripheral: CBPeripheral, error: Error?) {
        print("Disconnected from peripheral: \(peripheral.name ?? "Unknown")")
        delegate?.bluetoothManager(self, didDisconnectFromDevice: peripheral)
        
        if let error = error {
            delegate?.bluetoothManager(self, didEncounterError: error)
        }
        
        connectedPeripheral = nil
        controlCharacteristic = nil
        statusCharacteristic = nil
    }
    
    func centralManager(_ central: CBCentralManager, didFailToConnect peripheral: CBPeripheral, error: Error?) {
        print("Failed to connect to peripheral: \(error?.localizedDescription ?? "Unknown error")")
        if let error = error {
            delegate?.bluetoothManager(self, didEncounterError: error)
        }
    }
}

// MARK: - CBPeripheralDelegate

extension BluetoothManager: CBPeripheralDelegate {
    func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
        if let error = error {
            print("Error discovering services: \(error.localizedDescription)")
            delegate?.bluetoothManager(self, didEncounterError: error)
            return
        }
        
        guard let services = peripheral.services else { return }
        
        for service in services {
            if service.uuid == BluetoothConstants.serviceUUID {
                peripheral.discoverCharacteristics([
                    BluetoothConstants.controlCharacteristicUUID,
                    BluetoothConstants.statusCharacteristicUUID
                ], for: service)
            }
        }
    }
    
    func peripheral(_ peripheral: CBPeripheral, didDiscoverCharacteristicsFor service: CBService, error: Error?) {
        if let error = error {
            print("Error discovering characteristics: \(error.localizedDescription)")
            delegate?.bluetoothManager(self, didEncounterError: error)
            return
        }
        
        guard let characteristics = service.characteristics else { return }
        
        for characteristic in characteristics {
            switch characteristic.uuid {
            case BluetoothConstants.controlCharacteristicUUID:
                controlCharacteristic = characteristic
                print("Found control characteristic")
                
            case BluetoothConstants.statusCharacteristicUUID:
                statusCharacteristic = characteristic
                peripheral.setNotifyValue(true, for: characteristic)
                print("Found status characteristic")
                
            default:
                break
            }
        }
    }
    
    func peripheral(_ peripheral: CBPeripheral, didUpdateValueFor characteristic: CBCharacteristic, error: Error?) {
        if let error = error {
            print("Error updating value: \(error.localizedDescription)")
            delegate?.bluetoothManager(self, didEncounterError: error)
            return
        }
        
        guard let data = characteristic.value, !data.isEmpty else { return }
        
        switch characteristic.uuid {
        case BluetoothConstants.statusCharacteristicUUID:
            if let status = MirroringStatus(rawValue: data[0]) {
                delegate?.bluetoothManager(self, didUpdateStatus: status)
            }
            
        default:
            break
        }
    }
}

// MARK: - CBPeripheralManagerDelegate

extension BluetoothManager: CBPeripheralManagerDelegate {
    func peripheralManagerDidUpdateState(_ peripheral: CBPeripheralManager) {
        switch peripheral.state {
        case .poweredOn:
            print("Peripheral Manager powered on")
            setupService()
        case .poweredOff:
            print("Peripheral Manager powered off")
        case .resetting:
            print("Peripheral Manager resetting")
        case .unauthorized:
            print("Peripheral Manager unauthorized")
        case .unsupported:
            print("Peripheral Manager unsupported")
        case .unknown:
            print("Peripheral Manager unknown state")
        @unknown default:
            print("Peripheral Manager unknown state")
        }
    }
    
    private func setupService() {
        // 创建控制特征
        controlChar = CBMutableCharacteristic(
            type: BluetoothConstants.controlCharacteristicUUID,
            properties: [.write, .writeWithoutResponse],
            value: nil,
            permissions: [.writeable]
        )
        
        // 创建状态特征
        statusChar = CBMutableCharacteristic(
            type: BluetoothConstants.statusCharacteristicUUID,
            properties: [.read, .notify],
            value: nil,
            permissions: [.readable]
        )
        
        // 创建服务
        mirrorService = CBMutableService(type: BluetoothConstants.serviceUUID, primary: true)
        mirrorService?.characteristics = [controlChar!, statusChar!]
        
        // 添加服务
        peripheralManager?.add(mirrorService!)
        
        // 服务添加成功后，更新状态特征值
        // 注意：这里不能直接调用updateValue，需要等待didAdd service回调
    }
    
    func peripheralManager(_ peripheral: CBPeripheralManager, didAdd service: CBService, error: Error?) {
        if let error = error {
            print("Error adding service: \(error.localizedDescription)")
            delegate?.bluetoothManager(self, didEncounterError: error)
            return
        }
        
        print("Service added successfully")
        
        // 服务添加成功后，更新状态特征值
        if let characteristic = statusChar {
            let data = Data([currentStatus.rawValue])
            peripheral.updateValue(data, for: characteristic, onSubscribedCentrals: nil)
        }
    }
    
    func peripheralManager(_ peripheral: CBPeripheralManager, didReceiveWrite requests: [CBATTRequest]) {
        for request in requests {
            if request.characteristic.uuid == BluetoothConstants.controlCharacteristicUUID {
                if let data = request.value, !data.isEmpty {
                    if let command = ControlCommand(rawValue: data[0]) {
                        delegate?.bluetoothManager(self, didReceiveCommand: command)
                    }
                }
                peripheral.respond(to: request, withResult: .success)
            }
        }
    }
    
    func peripheralManagerDidStartAdvertising(_ peripheral: CBPeripheralManager, error: Error?) {
        if let error = error {
            print("Error starting advertising: \(error.localizedDescription)")
            delegate?.bluetoothManager(self, didEncounterError: error)
        } else {
            print("Started advertising successfully")
        }
    }
}
