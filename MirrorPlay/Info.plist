<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- 应用场景配置 -->
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
					<key>UISceneStoryboardFile</key>
					<string>Main</string>
				</dict>
			</array>
		</dict>
	</dict>

	<!-- 权限说明 -->
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>MirrorPlay需要使用蓝牙来连接和控制投屏设备</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>MirrorPlay需要使用蓝牙来连接和控制投屏设备</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>MirrorPlay需要访问本地网络来传输投屏数据</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>MirrorPlay需要访问麦克风来录制音频</string>

	<!-- 网络配置 -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>

	<!-- 后台模式 -->
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>background-processing</string>
	</array>

	<!-- 支持的设备方向 -->
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>

	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>

	<!-- 应用图标和启动屏幕 -->
	<key>CFBundleIcons</key>
	<dict>
		<key>CFBundlePrimaryIcon</key>
		<dict>
			<key>CFBundleIconFiles</key>
			<array>
				<string>AppIcon</string>
			</array>
		</dict>
	</dict>

	<!-- 应用信息 -->
	<key>CFBundleDisplayName</key>
	<string>MirrorPlay</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>

	<!-- 最低系统版本 -->
	<key>LSMinimumSystemVersion</key>
	<string>13.0</string>
</dict>
</plist>
