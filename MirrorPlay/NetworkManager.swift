//
//  NetworkManager.swift
//  MirrorPlay - 网络传输管理器
//
//  这个文件负责投屏应用的网络通信功能，包括：
//  1. 创建TCP服务器（发送端）接收客户端连接
//  2. 创建TCP客户端（接收端）连接到服务器
//  3. 定义数据包格式和传输协议
//  4. 处理视频、音频、控制数据的网络传输
//
//  网络架构：
//  发送端 = TCP服务器（监听8888端口）
//  接收端 = TCP客户端（连接到发送端IP:8888）
//

import Foundation
import Network  // iOS 13+的现代网络框架

// MARK: - 数据包类型定义
// 定义不同类型的数据包，用于区分传输的内容

enum PacketType: UInt8 {
    case videoFrame = 0x01      // 视频帧数据
    case audioFrame = 0x02      // 音频帧数据
    case controlCommand = 0x03  // 控制命令（开始/停止等）
    case heartbeat = 0x04       // 心跳包（保持连接活跃）
}

// MARK: - 数据包结构
// 定义网络传输的数据包格式，确保数据的完整性和正确解析

struct DataPacket {
    let type: PacketType        // 数据包类型（1字节）
    let timestamp: UInt64       // 时间戳（8字节，毫秒）
    let dataSize: UInt32        // 数据大小（4字节）
    let data: Data              // 实际数据（变长）

    // 将数据包序列化为二进制数据，用于网络传输
    func serialize() -> Data {
        var packet = Data()

        // 添加包类型（1字节）
        packet.append(type.rawValue)

        // 添加时间戳（8字节，小端序）
        var timestampBytes = timestamp
        packet.append(Data(bytes: &timestampBytes, count: 8))

        // 添加数据大小（4字节，小端序）
        var dataSizeBytes = dataSize
        packet.append(Data(bytes: &dataSizeBytes, count: 4))

        // 添加实际数据
        packet.append(data)

        return packet
    }

    // 从二进制数据反序列化为数据包对象
    static func deserialize(from data: Data) -> DataPacket? {
        // 检查最小包头长度：1(type) + 8(timestamp) + 4(dataSize) = 13字节
        guard data.count >= 13 else {
            print("❌ 数据包太短，无法解析")
            return nil
        }

        // 解析包类型
        let type = PacketType(rawValue: data[0])
        guard let packetType = type else {
            print("❌ 未知的数据包类型: \(data[0])")
            return nil
        }

        // 解析时间戳（8字节）
        let timestamp = data.subdata(in: 1..<9).withUnsafeBytes {
            $0.load(as: UInt64.self)
        }

        // 解析数据大小（4字节）
        let dataSize = data.subdata(in: 9..<13).withUnsafeBytes {
            $0.load(as: UInt32.self)
        }

        // 检查数据完整性
        guard data.count >= 13 + Int(dataSize) else {
            print("❌ 数据包不完整，期望\(13 + Int(dataSize))字节，实际\(data.count)字节")
            return nil
        }

        // 提取实际数据
        let payload = data.subdata(in: 13..<(13 + Int(dataSize)))

        return DataPacket(type: packetType, timestamp: timestamp, dataSize: dataSize, data: payload)
    }
}

// MARK: - 网络管理器代理协议
// 定义网络事件的回调接口，让使用者能够响应各种网络事件

protocol NetworkManagerDelegate: AnyObject {
    // 接收到视频帧数据时调用
    func networkManager(_ manager: NetworkManager, didReceiveVideoFrame data: Data, timestamp: UInt64)

    // 接收到音频帧数据时调用
    func networkManager(_ manager: NetworkManager, didReceiveAudioFrame data: Data, timestamp: UInt64)

    // 接收到控制命令时调用
    func networkManager(_ manager: NetworkManager, didReceiveControlCommand data: Data)

    // 有客户端连接时调用
    func networkManager(_ manager: NetworkManager, clientDidConnect client: String)

    // 客户端断开连接时调用
    func networkManager(_ manager: NetworkManager, clientDidDisconnect client: String)

    // 网络错误时调用
    func networkManager(_ manager: NetworkManager, didEncounterError error: Error)
}

// MARK: - 网络管理器主类
class NetworkManager {
    // 代理对象 - 接收网络事件通知
    weak var delegate: NetworkManagerDelegate?

    // MARK: - 服务器相关属性（发送端使用）

    // 网络监听器 - 用于接收客户端连接
    private var listener: NWListener?

    // 连接字典 - 存储所有已连接的客户端
    private var connections: [String: NWConnection] = [:]

    // 网络操作队列 - 在后台线程处理网络操作
    private let queue = DispatchQueue(label: "NetworkManager", qos: .userInitiated)

    // 服务器运行状态
    private var isServerRunning = false

    // MARK: - 网络配置

    // 服务器监听端口 - 发送端在此端口等待连接
    private var serverPort: UInt16 = 8888
    
    // 获取当前服务器端口
    var currentServerPort: UInt16 {
        return serverPort
    }
    
    // 动态端口分配
    private func findAvailablePort() -> UInt16 {
        for port in 8888...9000 {
            if !isPortInUse(UInt16(port)) {
                return UInt16(port)
            }
        }
        return 8888 // 如果都不可用，返回默认端口
    }
    
    private func isPortInUse(_ port: UInt16) -> Bool {
        let socket = socket(AF_INET, SOCK_STREAM, 0)
        if socket == -1 { return true }
        
        var addr = sockaddr_in()
        addr.sin_family = sa_family_t(AF_INET)
        addr.sin_port = port.bigEndian
        addr.sin_addr.s_addr = INADDR_ANY
        
        let bindResult = withUnsafePointer(to: &addr) {
            $0.withMemoryRebound(to: sockaddr.self, capacity: 1) {
                Darwin.bind(socket, $0, socklen_t(MemoryLayout<sockaddr_in>.size))
            }
        }
        
        close(socket)
        return bindResult != 0
    }

    // 初始化
    init() {
        print("🌐 网络管理器初始化完成")
    }
    
    // MARK: - 服务器功能
    
    func startServer() throws {
        guard !isServerRunning else { return }
        
        // 动态分配端口
        serverPort = findAvailablePort()
        print("🔌 尝试使用端口: \(serverPort)")
        
        let parameters = NWParameters.tcp
        parameters.allowLocalEndpointReuse = true
        
        listener = try NWListener(
            using: parameters,
            on: NWEndpoint.Port(rawValue: serverPort)!
        )
        
        listener?.newConnectionHandler = { [weak self] connection in
            self?.handleNewConnection(connection)
        }
        
        listener?.stateUpdateHandler = { [weak self] state in
            switch state {
            case .ready:
                print("Server ready on port \(self?.serverPort ?? 0)")
                self?.isServerRunning = true
                
            case .failed(let error):
                print("Server failed: \(error)")
                self?.delegate?.networkManager(self!, didEncounterError: error)
            case .cancelled:
                print("Server cancelled")
                self?.isServerRunning = false
            default:
                break
            }
        }
        
        listener?.start(queue: queue)
    }
    
    func stopServer() {
        listener?.cancel()
        listener = nil
        
        // 断开所有连接
        for connection in connections.values {
            connection.cancel()
        }
        connections.removeAll()
        isServerRunning = false
    }
    
    private func handleNewConnection(_ connection: NWConnection) {
        let clientId = UUID().uuidString
        connections[clientId] = connection
        
        connection.stateUpdateHandler = { [weak self] state in
            switch state {
            case .ready:
                print("Client connected: \(clientId)")
                self?.delegate?.networkManager(self!, clientDidConnect: clientId)
                self?.startReceiving(connection: connection, clientId: clientId)
            case .failed(let error):
                print("Connection failed: \(error)")
                self?.connections.removeValue(forKey: clientId)
                self?.delegate?.networkManager(self!, didEncounterError: error)
            case .cancelled:
                print("Client disconnected: \(clientId)")
                self?.connections.removeValue(forKey: clientId)
                self?.delegate?.networkManager(self!, clientDidDisconnect: clientId)
            default:
                break
            }
        }
        
        connection.start(queue: queue)
    }
    
    private func startReceiving(connection: NWConnection, clientId: String) {
        // 先读取包头（13字节）
        connection.receive(minimumIncompleteLength: 13, maximumLength: 13) { [weak self] data, _, isComplete, error in
            if let error = error {
                print("Receive error: \(error)")
                self?.delegate?.networkManager(self!, didEncounterError: error)
                return
            }
            
            guard let headerData = data, headerData.count == 13 else {
                if isComplete {
                    self?.connections.removeValue(forKey: clientId)
                    self?.delegate?.networkManager(self!, clientDidDisconnect: clientId)
                }
                return
            }
            
            // 解析包头获取数据大小
            let dataSize = headerData.subdata(in: 9..<13).withUnsafeBytes { $0.load(as: UInt32.self) }
            
            print("📦 接收到数据包头部，数据大小: \(dataSize) 字节")
            
            // 读取完整数据包
            self?.receiveCompletePacket(connection: connection, clientId: clientId, headerData: headerData, dataSize: Int(dataSize))
        }
    }
    
    private func receiveCompletePacket(connection: NWConnection, clientId: String, headerData: Data, dataSize: Int) {
        connection.receive(minimumIncompleteLength: dataSize, maximumLength: dataSize) { [weak self] data, _, isComplete, error in
            if let error = error {
                print("Receive payload error: \(error)")
                self?.delegate?.networkManager(self!, didEncounterError: error)
                return
            }
            
            guard let payloadData = data, payloadData.count == dataSize else {
                print("❌ 数据包不完整，期望\(dataSize)字节，实际\(data?.count ?? 0)字节")
                if isComplete {
                    self?.connections.removeValue(forKey: clientId)
                    self?.delegate?.networkManager(self!, clientDidDisconnect: clientId)
                }
                return
            }
            
            // 组合完整数据包
            var completeData = headerData
            completeData.append(payloadData)
            
            print("✅ 接收到完整数据包，总大小: \(completeData.count) 字节")
            
            // 解析数据包
            if let packet = DataPacket.deserialize(from: completeData) {
                self?.handleReceivedPacket(packet)
            } else {
                print("❌ 数据包解析失败")
            }
            
            // 继续接收下一个数据包
            self?.startReceiving(connection: connection, clientId: clientId)
        }
    }
    
    private func handleReceivedPacket(_ packet: DataPacket) {
        switch packet.type {
        case .videoFrame:
            delegate?.networkManager(self, didReceiveVideoFrame: packet.data, timestamp: packet.timestamp)
        case .audioFrame:
            delegate?.networkManager(self, didReceiveAudioFrame: packet.data, timestamp: packet.timestamp)
        case .controlCommand:
            delegate?.networkManager(self, didReceiveControlCommand: packet.data)
        case .heartbeat:
            // 处理心跳包
            break
        }
    }
    
    // MARK: - 客户端功能
    
    func connectToServer(host: String, port: UInt16? = nil, completion: @escaping (Result<Void, Error>) -> Void) {
        let targetPort = port ?? 8888
        let endpoint = NWEndpoint.hostPort(host: NWEndpoint.Host(host), port: NWEndpoint.Port(rawValue: targetPort)!)
        let connection = NWConnection(to: endpoint, using: .tcp)
        
        let clientId = "client-\(UUID().uuidString)"
        connections[clientId] = connection
        
        connection.stateUpdateHandler = { state in
            switch state {
            case .ready:
                print("Connected to server")
                completion(.success(()))
                self.startReceiving(connection: connection, clientId: clientId)
            case .failed(let error):
                print("Connection failed: \(error)")
                completion(.failure(error))
            case .cancelled:
                print("Connection cancelled")
                self.connections.removeValue(forKey: clientId)
            default:
                break
            }
        }
        
        connection.start(queue: queue)
    }
    
    // MARK: - 数据发送
    
    func sendVideoFrame(_ data: Data, timestamp: UInt64) {
        print("📤 发送视频帧: \(data.count) 字节, 时间戳: \(timestamp)")
        let packet = DataPacket(type: .videoFrame, timestamp: timestamp, dataSize: UInt32(data.count), data: data)
        sendPacket(packet)
    }
    
    func sendAudioFrame(_ data: Data, timestamp: UInt64) {
        print("📤 发送音频帧: \(data.count) 字节, 时间戳: \(timestamp)")
        let packet = DataPacket(type: .audioFrame, timestamp: timestamp, dataSize: UInt32(data.count), data: data)
        sendPacket(packet)
    }
    
    func sendControlCommand(_ data: Data) {
        print("📤 发送控制命令: \(data.count) 字节")
        let packet = DataPacket(type: .controlCommand, timestamp: UInt64(Date().timeIntervalSince1970 * 1000), dataSize: UInt32(data.count), data: data)
        sendPacket(packet)
    }
    
    private func sendPacket(_ packet: DataPacket) {
        let data = packet.serialize()
        print("📦 发送数据包: 类型=\(packet.type), 大小=\(data.count)字节")
        
        for connection in connections.values {
            connection.send(content: data, completion: .contentProcessed { error in
                if let error = error {
                    print("❌ 发送错误: \(error)")
                    self.delegate?.networkManager(self, didEncounterError: error)
                } else {
                    print("✅ 数据包发送成功")
                }
            })
        }
    }
    
    // MARK: - 工具方法
    
    func getLocalIPAddress() -> String? {
        var address: String?
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        
        if getifaddrs(&ifaddr) == 0 {
            var ptr = ifaddr
            while ptr != nil {
                defer { ptr = ptr?.pointee.ifa_next }
                
                let interface = ptr?.pointee
                let addrFamily = interface?.ifa_addr.pointee.sa_family
                
                if addrFamily == UInt8(AF_INET) {
                    let name = String(cString: (interface?.ifa_name)!)
                    if name == "en0" || name == "pdp_ip0" {
                        var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                        getnameinfo(interface?.ifa_addr, socklen_t((interface?.ifa_addr.pointee.sa_len)!),
                                   &hostname, socklen_t(hostname.count),
                                   nil, socklen_t(0), NI_NUMERICHOST)
                        address = String(cString: hostname)
                    }
                }
            }
            freeifaddrs(ifaddr)
        }
        
        return address
    }
}
