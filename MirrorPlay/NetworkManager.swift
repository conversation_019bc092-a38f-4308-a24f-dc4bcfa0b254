//
//  NetworkManager.swift
//  MirrorPlay
//
//  Created by cf110 on 2025/7/24.
//

import Foundation
import Network

// 数据包类型定义
enum PacketType: UInt8 {
    case videoFrame = 0x01
    case audioFrame = 0x02
    case controlCommand = 0x03
    case heartbeat = 0x04
}

// 数据包结构
struct DataPacket {
    let type: PacketType
    let timestamp: UInt64
    let dataSize: UInt32
    let data: Data
    
    func serialize() -> Data {
        var packet = Data()
        packet.append(type.rawValue)
        packet.append(Data(bytes: &timestamp, count: 8))
        packet.append(Data(bytes: &dataSize, count: 4))
        packet.append(data)
        return packet
    }
    
    static func deserialize(from data: Data) -> DataPacket? {
        guard data.count >= 13 else { return nil }
        
        let type = PacketType(rawValue: data[0])
        guard let packetType = type else { return nil }
        
        let timestamp = data.subdata(in: 1..<9).withUnsafeBytes { $0.load(as: UInt64.self) }
        let dataSize = data.subdata(in: 9..<13).withUnsafeBytes { $0.load(as: UInt32.self) }
        
        guard data.count >= 13 + Int(dataSize) else { return nil }
        let payload = data.subdata(in: 13..<(13 + Int(dataSize)))
        
        return DataPacket(type: packetType, timestamp: timestamp, dataSize: dataSize, data: payload)
    }
}

// 网络管理器协议
protocol NetworkManagerDelegate: AnyObject {
    func networkManager(_ manager: NetworkManager, didReceiveVideoFrame data: Data, timestamp: UInt64)
    func networkManager(_ manager: NetworkManager, didReceiveAudioFrame data: Data, timestamp: UInt64)
    func networkManager(_ manager: NetworkManager, didReceiveControlCommand data: Data)
    func networkManager(_ manager: NetworkManager, clientDidConnect client: String)
    func networkManager(_ manager: NetworkManager, clientDidDisconnect client: String)
    func networkManager(_ manager: NetworkManager, didEncounterError error: Error)
}

class NetworkManager {
    weak var delegate: NetworkManagerDelegate?
    
    private var listener: NWListener?
    private var connections: [String: NWConnection] = [:]
    private let queue = DispatchQueue(label: "NetworkManager", qos: .userInitiated)
    private var isServerRunning = false
    
    // 服务器端口
    private let serverPort: UInt16 = 8888
    
    init() {}
    
    // MARK: - 服务器功能
    
    func startServer() throws {
        guard !isServerRunning else { return }
        
        let parameters = NWParameters.tcp
        parameters.allowLocalEndpointReuse = true
        
        listener = try NWListener(using: parameters, on: NWEndpoint.Port(serverPort)!)
        
        listener?.newConnectionHandler = { [weak self] connection in
            self?.handleNewConnection(connection)
        }
        
        listener?.stateUpdateHandler = { [weak self] state in
            switch state {
            case .ready:
                print("Server ready on port \(self?.serverPort ?? 0)")
                self?.isServerRunning = true
            case .failed(let error):
                print("Server failed: \(error)")
                self?.delegate?.networkManager(self!, didEncounterError: error)
            case .cancelled:
                print("Server cancelled")
                self?.isServerRunning = false
            default:
                break
            }
        }
        
        listener?.start(queue: queue)
    }
    
    func stopServer() {
        listener?.cancel()
        listener = nil
        
        // 断开所有连接
        for connection in connections.values {
            connection.cancel()
        }
        connections.removeAll()
        isServerRunning = false
    }
    
    private func handleNewConnection(_ connection: NWConnection) {
        let clientId = UUID().uuidString
        connections[clientId] = connection
        
        connection.stateUpdateHandler = { [weak self] state in
            switch state {
            case .ready:
                print("Client connected: \(clientId)")
                self?.delegate?.networkManager(self!, clientDidConnect: clientId)
                self?.startReceiving(connection: connection, clientId: clientId)
            case .failed(let error):
                print("Connection failed: \(error)")
                self?.connections.removeValue(forKey: clientId)
                self?.delegate?.networkManager(self!, didEncounterError: error)
            case .cancelled:
                print("Client disconnected: \(clientId)")
                self?.connections.removeValue(forKey: clientId)
                self?.delegate?.networkManager(self!, clientDidDisconnect: clientId)
            default:
                break
            }
        }
        
        connection.start(queue: queue)
    }
    
    private func startReceiving(connection: NWConnection, clientId: String) {
        // 先读取包头（13字节）
        connection.receive(minimumIncompleteLength: 13, maximumLength: 13) { [weak self] data, _, isComplete, error in
            if let error = error {
                print("Receive error: \(error)")
                self?.delegate?.networkManager(self!, didEncounterError: error)
                return
            }
            
            guard let headerData = data, headerData.count == 13 else {
                if isComplete {
                    self?.connections.removeValue(forKey: clientId)
                    self?.delegate?.networkManager(self!, clientDidDisconnect: clientId)
                }
                return
            }
            
            // 解析包头获取数据大小
            let dataSize = headerData.subdata(in: 9..<13).withUnsafeBytes { $0.load(as: UInt32.self) }
            
            // 读取完整数据包
            self?.receiveCompletePacket(connection: connection, clientId: clientId, headerData: headerData, dataSize: Int(dataSize))
        }
    }
    
    private func receiveCompletePacket(connection: NWConnection, clientId: String, headerData: Data, dataSize: Int) {
        connection.receive(minimumIncompleteLength: dataSize, maximumLength: dataSize) { [weak self] data, _, isComplete, error in
            if let error = error {
                print("Receive payload error: \(error)")
                self?.delegate?.networkManager(self!, didEncounterError: error)
                return
            }
            
            guard let payloadData = data, payloadData.count == dataSize else {
                if isComplete {
                    self?.connections.removeValue(forKey: clientId)
                    self?.delegate?.networkManager(self!, clientDidDisconnect: clientId)
                }
                return
            }
            
            // 组合完整数据包
            var completeData = headerData
            completeData.append(payloadData)
            
            // 解析数据包
            if let packet = DataPacket.deserialize(from: completeData) {
                self?.handleReceivedPacket(packet)
            }
            
            // 继续接收下一个数据包
            self?.startReceiving(connection: connection, clientId: clientId)
        }
    }
    
    private func handleReceivedPacket(_ packet: DataPacket) {
        switch packet.type {
        case .videoFrame:
            delegate?.networkManager(self, didReceiveVideoFrame: packet.data, timestamp: packet.timestamp)
        case .audioFrame:
            delegate?.networkManager(self, didReceiveAudioFrame: packet.data, timestamp: packet.timestamp)
        case .controlCommand:
            delegate?.networkManager(self, didReceiveControlCommand: packet.data)
        case .heartbeat:
            // 处理心跳包
            break
        }
    }
    
    // MARK: - 客户端功能
    
    func connectToServer(host: String, port: UInt16 = 8888, completion: @escaping (Result<Void, Error>) -> Void) {
        let endpoint = NWEndpoint.hostPort(host: NWEndpoint.Host(host), port: NWEndpoint.Port(port)!)
        let connection = NWConnection(to: endpoint, using: .tcp)
        
        let clientId = "client-\(UUID().uuidString)"
        connections[clientId] = connection
        
        connection.stateUpdateHandler = { state in
            switch state {
            case .ready:
                print("Connected to server")
                completion(.success(()))
                self.startReceiving(connection: connection, clientId: clientId)
            case .failed(let error):
                print("Connection failed: \(error)")
                completion(.failure(error))
            case .cancelled:
                print("Connection cancelled")
                self.connections.removeValue(forKey: clientId)
            default:
                break
            }
        }
        
        connection.start(queue: queue)
    }
    
    // MARK: - 数据发送
    
    func sendVideoFrame(_ data: Data, timestamp: UInt64) {
        let packet = DataPacket(type: .videoFrame, timestamp: timestamp, dataSize: UInt32(data.count), data: data)
        sendPacket(packet)
    }
    
    func sendAudioFrame(_ data: Data, timestamp: UInt64) {
        let packet = DataPacket(type: .audioFrame, timestamp: timestamp, dataSize: UInt32(data.count), data: data)
        sendPacket(packet)
    }
    
    func sendControlCommand(_ data: Data) {
        let packet = DataPacket(type: .controlCommand, timestamp: UInt64(Date().timeIntervalSince1970 * 1000), dataSize: UInt32(data.count), data: data)
        sendPacket(packet)
    }
    
    private func sendPacket(_ packet: DataPacket) {
        let data = packet.serialize()
        
        for connection in connections.values {
            connection.send(content: data, completion: .contentProcessed { error in
                if let error = error {
                    print("Send error: \(error)")
                    self.delegate?.networkManager(self, didEncounterError: error)
                }
            })
        }
    }
    
    // MARK: - 工具方法
    
    func getLocalIPAddress() -> String? {
        var address: String?
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        
        if getifaddrs(&ifaddr) == 0 {
            var ptr = ifaddr
            while ptr != nil {
                defer { ptr = ptr?.pointee.ifa_next }
                
                let interface = ptr?.pointee
                let addrFamily = interface?.ifa_addr.pointee.sa_family
                
                if addrFamily == UInt8(AF_INET) {
                    let name = String(cString: (interface?.ifa_name)!)
                    if name == "en0" || name == "pdp_ip0" {
                        var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                        getnameinfo(interface?.ifa_addr, socklen_t((interface?.ifa_addr.pointee.sa_len)!),
                                   &hostname, socklen_t(hostname.count),
                                   nil, socklen_t(0), NI_NUMERICHOST)
                        address = String(cString: hostname)
                    }
                }
            }
            freeifaddrs(ifaddr)
        }
        
        return address
    }
}
