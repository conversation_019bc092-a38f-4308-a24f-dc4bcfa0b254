//
//  PlayerViewController.swift
//  MirrorPlay
//
//  Created by cf110 on 2025/7/24.
//

import UIKit
import AVFoundation
import CoreVideo

class PlayerViewController: UIViewController {
    
    // MARK: - UI Elements
    
    @IBOutlet weak var videoView: UIView!
    @IBOutlet weak var statusLabel: UILabel!
    @IBOutlet weak var connectButton: UIButton!
    @IBOutlet weak var ipTextField: UITextField!
    @IBOutlet weak var bluetoothButton: UIButton!
    @IBOutlet weak var qualityLabel: UILabel!
    
    // MARK: - Properties
    
    private var networkManager: NetworkManager?
    private var bluetoothManager: BluetoothManager?
    private var videoDecoder: VideoDecoder?
    private var audioDecoder: AudioDecoder?
    private var audioPlayer: AudioPlayer?
    
    // 视频显示
    private var displayLayer: AVSampleBufferDisplayLayer?
    private var isConnected = false
    
    // 统计信息
    private var frameCount = 0
    private var lastFrameTime: TimeInterval = 0
    private var fps: Double = 0
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupManagers()
        setupVideoDisplay()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        disconnect()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        title = "MirrorPlay 接收端"
        
        // 配置状态标签
        statusLabel.text = "未连接"
        statusLabel.textAlignment = .center
        statusLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        
        // 配置连接按钮
        connectButton.setTitle("连接", for: .normal)
        connectButton.backgroundColor = .systemBlue
        connectButton.layer.cornerRadius = 8
        connectButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        
        // 配置蓝牙按钮
        bluetoothButton.setTitle("蓝牙连接", for: .normal)
        bluetoothButton.backgroundColor = .systemGreen
        bluetoothButton.layer.cornerRadius = 8
        bluetoothButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        
        // 配置IP输入框
        ipTextField.placeholder = "输入服务器IP地址"
        ipTextField.borderStyle = .roundedRect
        ipTextField.keyboardType = .numbersAndPunctuation
        ipTextField.text = "*************" // 默认IP
        
        // 配置质量标签
        qualityLabel.text = "质量: --"
        qualityLabel.font = UIFont.systemFont(ofSize: 14)
        
        // 配置视频视图
        videoView.backgroundColor = .black
        videoView.layer.cornerRadius = 8
    }
    
    private func setupManagers() {
        // 设置网络管理器
        networkManager = NetworkManager()
        networkManager?.delegate = self
        
        // 设置蓝牙管理器
        bluetoothManager = BluetoothManager()
        bluetoothManager?.delegate = self
        
        // 设置解码器
        videoDecoder = VideoDecoder()
        videoDecoder?.delegate = self
        
        audioDecoder = AudioDecoder()
        audioDecoder?.delegate = self
        
        // 设置音频播放器
        audioPlayer = AudioPlayer()
    }
    
    private func setupVideoDisplay() {
        // 创建视频显示层
        displayLayer = AVSampleBufferDisplayLayer()
        
        guard let layer = displayLayer else { return }
        
        layer.frame = videoView.bounds
        layer.videoGravity = .resizeAspect
        layer.backgroundColor = UIColor.black.cgColor
        
        videoView.layer.addSublayer(layer)
        
        // 监听视图大小变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(orientationChanged),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )
    }
    
    @objc private func orientationChanged() {
        DispatchQueue.main.async { [weak self] in
            self?.displayLayer?.frame = self?.videoView.bounds ?? .zero
        }
    }
    
    // MARK: - Actions
    
    @IBAction func connectButtonTapped(_ sender: UIButton) {
        if isConnected {
            disconnect()
        } else {
            connect()
        }
    }
    
    @IBAction func bluetoothButtonTapped(_ sender: UIButton) {
        if bluetoothManager?.isScanning == true {
            bluetoothManager?.stopScanning()
            bluetoothButton.setTitle("蓝牙连接", for: .normal)
            bluetoothButton.backgroundColor = .systemGreen
        } else {
            bluetoothManager?.startScanning()
            bluetoothButton.setTitle("停止扫描", for: .normal)
            bluetoothButton.backgroundColor = .systemRed
        }
    }
    
    // MARK: - Connection Management
    
    private func connect() {
        guard let ipAddress = ipTextField.text, !ipAddress.isEmpty else {
            showAlert(title: "错误", message: "请输入服务器IP地址")
            return
        }
        
        statusLabel.text = "连接中..."
        connectButton.isEnabled = false
        
        networkManager?.connectToServer(host: ipAddress) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    self?.onConnected()
                case .failure(let error):
                    self?.onConnectionFailed(error)
                }
            }
        }
    }
    
    private func disconnect() {
        // 停止解码器
        videoDecoder?.stopDecoding()
        audioDecoder?.stopDecoding()
        audioPlayer?.stop()
        
        // 断开网络连接
        networkManager?.stopServer()
        
        // 停止蓝牙
        bluetoothManager?.stopScanning()
        bluetoothManager?.disconnect()
        
        // 清空显示
        displayLayer?.flushAndRemoveImage()
        
        // 更新UI
        isConnected = false
        updateUI()
    }
    
    private func onConnected() {
        isConnected = true
        
        // 启动解码器
        do {
            try videoDecoder?.startDecoding()
            try audioDecoder?.startDecoding()
            try audioPlayer?.start()
        } catch {
            showAlert(title: "错误", message: "启动解码器失败: \(error.localizedDescription)")
            disconnect()
            return
        }
        
        updateUI()
    }
    
    private func onConnectionFailed(_ error: Error) {
        showAlert(title: "连接失败", message: error.localizedDescription)
        updateUI()
    }
    
    private func updateUI() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if self.isConnected {
                self.statusLabel.text = "已连接 - FPS: \(String(format: "%.1f", self.fps))"
                self.statusLabel.textColor = .systemGreen
                self.connectButton.setTitle("断开", for: .normal)
                self.connectButton.backgroundColor = .systemRed
            } else {
                self.statusLabel.text = "未连接"
                self.statusLabel.textColor = .label
                self.connectButton.setTitle("连接", for: .normal)
                self.connectButton.backgroundColor = .systemBlue
            }
            
            self.connectButton.isEnabled = true
        }
    }
    
    // MARK: - Video Display
    
    private func displayVideoFrame(_ pixelBuffer: CVPixelBuffer, timestamp: UInt64) {
        guard let layer = displayLayer else { return }
        
        // 创建CMSampleBuffer用于显示
        var formatDescription: CMVideoFormatDescription?
        let status1 = CMVideoFormatDescriptionCreateForImageBuffer(
            allocator: kCFAllocatorDefault,
            imageBuffer: pixelBuffer,
            formatDescriptionOut: &formatDescription
        )
        
        guard status1 == noErr, let formatDesc = formatDescription else {
            print("Failed to create format description: \(status1)")
            return
        }
        
        var sampleBuffer: CMSampleBuffer?
        let presentationTime = CMTime(value: Int64(timestamp), timescale: 1000)
        
        let status2 = CMSampleBufferCreateReadyWithImageBuffer(
            allocator: kCFAllocatorDefault,
            imageBuffer: pixelBuffer,
            formatDescription: formatDesc,
            sampleTiming: &CMSampleTimingInfo(
                duration: CMTime.invalid,
                presentationTimeStamp: presentationTime,
                decodeTimeStamp: CMTime.invalid
            ),
            sampleBufferOut: &sampleBuffer
        )
        
        guard status2 == noErr, let sample = sampleBuffer else {
            print("Failed to create sample buffer: \(status2)")
            return
        }
        
        // 在主线程显示
        DispatchQueue.main.async {
            if layer.isReadyForMoreMediaData {
                layer.enqueue(sample)
                self.updateFrameRate()
            }
        }
    }
    
    private func updateFrameRate() {
        frameCount += 1
        let currentTime = CACurrentMediaTime()
        
        if lastFrameTime == 0 {
            lastFrameTime = currentTime
        } else if currentTime - lastFrameTime >= 1.0 {
            fps = Double(frameCount) / (currentTime - lastFrameTime)
            frameCount = 0
            lastFrameTime = currentTime
            
            // 更新UI
            updateUI()
        }
    }
    
    // MARK: - Utility
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - NetworkManagerDelegate

extension PlayerViewController: NetworkManagerDelegate {
    func networkManager(_ manager: NetworkManager, didReceiveVideoFrame data: Data, timestamp: UInt64) {
        videoDecoder?.decodeFrame(data, timestamp: timestamp)
    }

    func networkManager(_ manager: NetworkManager, didReceiveAudioFrame data: Data, timestamp: UInt64) {
        audioDecoder?.decodeAudioFrame(data, timestamp: timestamp)
    }

    func networkManager(_ manager: NetworkManager, didReceiveControlCommand data: Data) {
        guard let command = String(data: data, encoding: .utf8) else { return }

        DispatchQueue.main.async { [weak self] in
            switch command {
            case "streaming":
                self?.statusLabel.text = "正在接收投屏"
            case "idle":
                self?.statusLabel.text = "发送端空闲"
            default:
                print("Unknown control command: \(command)")
            }
        }
    }

    func networkManager(_ manager: NetworkManager, clientDidConnect client: String) {
        print("Server client connected: \(client)")
    }

    func networkManager(_ manager: NetworkManager, clientDidDisconnect client: String) {
        print("Server client disconnected: \(client)")
    }

    func networkManager(_ manager: NetworkManager, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "网络错误", message: error.localizedDescription)
            self?.disconnect()
        }
    }
}

// MARK: - BluetoothManagerDelegate

extension PlayerViewController: BluetoothManagerDelegate {
    func bluetoothManager(_ manager: BluetoothManager, didReceiveCommand command: ControlCommand) {
        DispatchQueue.main.async { [weak self] in
            switch command {
            case .startMirroring:
                self?.statusLabel.text = "开始投屏"
            case .stopMirroring:
                self?.statusLabel.text = "停止投屏"
            case .pauseMirroring:
                self?.statusLabel.text = "暂停投屏"
            case .resumeMirroring:
                self?.statusLabel.text = "恢复投屏"
            default:
                break
            }
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didUpdateStatus status: MirroringStatus) {
        DispatchQueue.main.async { [weak self] in
            switch status {
            case .idle:
                self?.qualityLabel.text = "状态: 空闲"
            case .connecting:
                self?.qualityLabel.text = "状态: 连接中"
            case .connected:
                self?.qualityLabel.text = "状态: 已连接"
            case .mirroring:
                self?.qualityLabel.text = "状态: 投屏中"
            case .paused:
                self?.qualityLabel.text = "状态: 已暂停"
            case .error:
                self?.qualityLabel.text = "状态: 错误"
            }
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didConnectToDevice device: CBPeripheral) {
        DispatchQueue.main.async { [weak self] in
            self?.bluetoothButton.setTitle("已连接", for: .normal)
            self?.bluetoothButton.backgroundColor = .systemGray
            self?.bluetoothButton.isEnabled = false
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didDisconnectFromDevice device: CBPeripheral) {
        DispatchQueue.main.async { [weak self] in
            self?.bluetoothButton.setTitle("蓝牙连接", for: .normal)
            self?.bluetoothButton.backgroundColor = .systemGreen
            self?.bluetoothButton.isEnabled = true
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "蓝牙错误", message: error.localizedDescription)
        }
    }
}

// MARK: - VideoDecoderDelegate

extension PlayerViewController: VideoDecoderDelegate {
    func videoDecoder(_ decoder: VideoDecoder, didDecodeFrame pixelBuffer: CVPixelBuffer, timestamp: UInt64) {
        displayVideoFrame(pixelBuffer, timestamp: timestamp)
    }

    func videoDecoder(_ decoder: VideoDecoder, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "视频解码错误", message: error.localizedDescription)
        }
    }
}

// MARK: - AudioDecoderDelegate

extension PlayerViewController: AudioDecoderDelegate {
    func audioDecoder(_ decoder: AudioDecoder, didDecodeFrame data: Data, timestamp: UInt64) {
        audioPlayer?.playAudioData(data)
    }

    func audioDecoder(_ decoder: AudioDecoder, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "音频解码错误", message: error.localizedDescription)
        }
    }
}
