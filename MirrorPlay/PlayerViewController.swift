//
//  PlayerViewController.swift
//  MirrorPlay - 接收端播放界面
//
//  这个文件是投屏应用的接收端界面，负责：
//  1. 连接到发送端设备
//  2. 接收并解码视频/音频数据
//  3. 在屏幕上播放投屏内容
//  4. 显示连接状态和播放信息
//

import UIKit
import AVFoundation
import CoreVideo
import CoreBluetooth

class PlayerViewController: UIViewController {

    // MARK: - UI Elements (界面元素)
    // 这些是接收端界面的各种控件，用代码创建

    // 视频显示区域 - 用来显示接收到的投屏画面
    private var videoView: UIView!

    // 状态显示标签 - 显示连接状态、帧率等信息
    private var statusLabel: UILabel!

    // 连接/断开按钮 - 控制与发送端的连接
    private var connectButton: UIButton!

    // IP地址输入框 - 用户输入发送端的IP地址
    private var ipTextField: UITextField!

    // 蓝牙连接按钮 - 通过蓝牙自动发现和连接
    private var bluetoothButton: UIButton!

    // 质量信息标签 - 显示当前播放质量和统计信息
    private var qualityLabel: UILabel!

    // 返回按钮 - 返回到发送端界面
    private var backButton: UIButton!

    // MARK: - Properties (属性变量)
    // 这些是接收端的核心功能模块和状态变量

    // 网络管理器 - 负责与发送端的网络通信
    private var networkManager: NetworkManager?

    // 蓝牙管理器 - 负责蓝牙设备发现和连接
    private var bluetoothManager: BluetoothManager?

    // 视频解码器 - 将接收到的H.264数据解码为可显示的图像
    private var videoDecoder: VideoDecoder?

    // 音频解码器 - 将接收到的AAC数据解码为可播放的音频
    private var audioDecoder: AudioDecoder?

    // 音频播放器 - 播放解码后的音频数据
    private var audioPlayer: AudioPlayer?

    // 视频显示层 - iOS系统提供的高效视频显示组件
    private var displayLayer: AVSampleBufferDisplayLayer?

    // 连接状态标志 - true表示已连接到发送端
    private var isConnected = false

    // 性能统计变量
    private var frameCount = 0  // 接收到的视频帧数量
    private var lastFrameTime: TimeInterval = 0  // 上次统计时间
    private var fps: Double = 0  // 当前帧率（每秒帧数）

    // MARK: - Lifecycle (生命周期方法)

    // 界面加载完成后调用
    override func viewDidLoad() {
        super.viewDidLoad()

        // 设置用户界面
        setupUI()

        // 初始化功能模块
        setupManagers()

        // 设置视频显示组件
        setupVideoDisplay()
    }

    // 界面即将消失时调用（比如用户返回上一页）
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)

        // 断开所有连接，释放资源
        disconnect()
    }

    // MARK: - Setup (初始化设置)

    // 设置用户界面
    private func setupUI() {
        // 设置导航栏标题
        title = "MirrorPlay - 接收端"

        // 设置背景颜色
        view.backgroundColor = UIColor.systemBackground

        // 创建所有界面元素
        createUIElements()

        // 设置界面布局
        setupConstraints()

        // 配置界面样式
        configureUIElements()
    }

    // 创建所有界面元素
    private func createUIElements() {
        // 创建视频显示区域
        videoView = UIView()
        videoView.backgroundColor = .black  // 黑色背景
        videoView.layer.cornerRadius = 12  // 圆角
        videoView.layer.masksToBounds = true  // 裁剪圆角

        // 创建状态显示标签
        statusLabel = UILabel()
        statusLabel.text = "📱 未连接"
        statusLabel.textAlignment = .center
        statusLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        statusLabel.textColor = .secondaryLabel

        // 创建IP地址输入框
        ipTextField = UITextField()
        ipTextField.placeholder = "输入发送端IP地址"
        ipTextField.borderStyle = .roundedRect
        ipTextField.keyboardType = .numbersAndPunctuation
        ipTextField.text = "*************"  // 默认IP地址
        ipTextField.font = UIFont.systemFont(ofSize: 16)
        ipTextField.textAlignment = .center

        // 创建连接按钮
        connectButton = UIButton(type: .system)
        connectButton.setTitle("🔗 连接", for: .normal)
        connectButton.backgroundColor = .systemBlue
        connectButton.setTitleColor(.white, for: .normal)
        connectButton.layer.cornerRadius = 12
        connectButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        connectButton.addTarget(self, action: #selector(connectButtonTapped), for: .touchUpInside)

        // 创建蓝牙连接按钮
        bluetoothButton = UIButton(type: .system)
        bluetoothButton.setTitle("📡 蓝牙扫描", for: .normal)
        bluetoothButton.backgroundColor = .systemGreen
        bluetoothButton.setTitleColor(.white, for: .normal)
        bluetoothButton.layer.cornerRadius = 12
        bluetoothButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        bluetoothButton.addTarget(self, action: #selector(bluetoothButtonTapped), for: .touchUpInside)

        // 创建质量信息标签
        qualityLabel = UILabel()
        qualityLabel.text = "📊 质量: 等待连接"
        qualityLabel.font = UIFont.systemFont(ofSize: 16)
        qualityLabel.textColor = .secondaryLabel
        qualityLabel.textAlignment = .center

        // 创建返回按钮
        backButton = UIButton(type: .system)
        backButton.setTitle("← 返回发送端", for: .normal)
        backButton.backgroundColor = .systemGray
        backButton.setTitleColor(.white, for: .normal)
        backButton.layer.cornerRadius = 12
        backButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        backButton.addTarget(self, action: #selector(backButtonTapped), for: .touchUpInside)

        // 将所有元素添加到主视图
        view.addSubview(videoView)
        view.addSubview(statusLabel)
        view.addSubview(ipTextField)
        view.addSubview(connectButton)
        view.addSubview(bluetoothButton)
        view.addSubview(qualityLabel)
        view.addSubview(backButton)
    }

    // 设置界面布局约束
    private func setupConstraints() {
        // 禁用自动调整大小掩码
        videoView.translatesAutoresizingMaskIntoConstraints = false
        statusLabel.translatesAutoresizingMaskIntoConstraints = false
        ipTextField.translatesAutoresizingMaskIntoConstraints = false
        connectButton.translatesAutoresizingMaskIntoConstraints = false
        bluetoothButton.translatesAutoresizingMaskIntoConstraints = false
        qualityLabel.translatesAutoresizingMaskIntoConstraints = false
        backButton.translatesAutoresizingMaskIntoConstraints = false

        // 设置所有约束
        NSLayoutConstraint.activate([
            // 视频显示区域 - 占据屏幕上半部分
            videoView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            videoView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            videoView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            videoView.heightAnchor.constraint(equalTo: view.heightAnchor, multiplier: 0.4),  // 40%的屏幕高度

            // 状态标签
            statusLabel.topAnchor.constraint(equalTo: videoView.bottomAnchor, constant: 20),
            statusLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            statusLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            statusLabel.heightAnchor.constraint(equalToConstant: 30),

            // IP输入框
            ipTextField.topAnchor.constraint(equalTo: statusLabel.bottomAnchor, constant: 20),
            ipTextField.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            ipTextField.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            ipTextField.heightAnchor.constraint(equalToConstant: 44),

            // 连接按钮
            connectButton.topAnchor.constraint(equalTo: ipTextField.bottomAnchor, constant: 20),
            connectButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            connectButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            connectButton.heightAnchor.constraint(equalToConstant: 50),

            // 蓝牙按钮
            bluetoothButton.topAnchor.constraint(equalTo: connectButton.bottomAnchor, constant: 15),
            bluetoothButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            bluetoothButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            bluetoothButton.heightAnchor.constraint(equalToConstant: 44),

            // 质量信息标签
            qualityLabel.topAnchor.constraint(equalTo: bluetoothButton.bottomAnchor, constant: 20),
            qualityLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            qualityLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            qualityLabel.heightAnchor.constraint(equalToConstant: 30),

            // 返回按钮 - 位于底部
            backButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -20),
            backButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            backButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            backButton.heightAnchor.constraint(equalToConstant: 44)
        ])
    }

    // 配置界面元素样式
    private func configureUIElements() {
        // 为按钮添加阴影效果
        [connectButton, bluetoothButton, backButton].forEach { button in
            button?.layer.shadowColor = UIColor.black.cgColor
            button?.layer.shadowOffset = CGSize(width: 0, height: 2)
            button?.layer.shadowOpacity = 0.1
            button?.layer.shadowRadius = 4
        }

        // 为视频区域添加边框
        videoView.layer.borderWidth = 1
        videoView.layer.borderColor = UIColor.systemGray4.cgColor
    }

    // 初始化功能模块
    private func setupManagers() {
        print("🔧 初始化接收端功能模块...")

        // 创建并设置网络管理器
        networkManager = NetworkManager()
        networkManager?.delegate = self  // 设置代理接收网络事件

        // 创建并设置蓝牙管理器
        bluetoothManager = BluetoothManager()
        bluetoothManager?.delegate = self  // 设置代理接收蓝牙事件

        // 创建并设置视频解码器
        videoDecoder = VideoDecoder()
        videoDecoder?.delegate = self  // 设置代理接收解码事件

        // 创建并设置音频解码器
        audioDecoder = AudioDecoder()
        audioDecoder?.delegate = self  // 设置代理接收解码事件

        // 创建音频播放器
        audioPlayer = AudioPlayer()

        print("✅ 功能模块初始化完成")
    }

    // 设置视频显示组件
    private func setupVideoDisplay() {
        print("🎬 设置视频显示组件...")

        // 创建AVSampleBufferDisplayLayer - 这是iOS提供的高效视频显示层
        displayLayer = AVSampleBufferDisplayLayer()

        guard let layer = displayLayer else {
            print("❌ 无法创建视频显示层")
            return
        }

        // 设置显示层的属性
        layer.frame = videoView.bounds  // 设置显示区域
        layer.videoGravity = .resizeAspect  // 保持视频比例，适应显示区域
        layer.backgroundColor = UIColor.black.cgColor  // 黑色背景

        // 将显示层添加到视频视图中
        videoView.layer.addSublayer(layer)

        // 监听设备旋转事件，以便调整显示层大小
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(orientationChanged),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )

        print("✅ 视频显示组件设置完成")
    }

    // 设备旋转时调整视频显示层大小
    @objc private func orientationChanged() {
        DispatchQueue.main.async { [weak self] in
            // 更新显示层的frame以适应新的视图大小
            self?.displayLayer?.frame = self?.videoView.bounds ?? .zero
            print("🔄 视频显示层大小已调整")
        }
    }

    // MARK: - Actions (用户交互事件处理)

    // 连接/断开按钮点击事件
    @objc private func connectButtonTapped(_ sender: UIButton) {
        if isConnected {
            // 如果已连接，则断开连接
            print("🔌 用户点击断开连接")
            disconnect()
        } else {
            // 如果未连接，则尝试连接
            print("🔌 用户点击连接")
            connect()
        }
    }

    // 蓝牙按钮点击事件
    @objc private func bluetoothButtonTapped(_ sender: UIButton) {
        // 检查当前是否正在扫描蓝牙设备
        if bluetoothManager?.isScanning == true {
            // 如果正在扫描，则停止扫描
            print("📡 停止蓝牙扫描")
            bluetoothManager?.stopScanning()
            bluetoothButton.setTitle("📡 蓝牙扫描", for: .normal)
            bluetoothButton.backgroundColor = .systemGreen
        } else {
            // 如果未在扫描，则开始扫描
            print("📡 开始蓝牙扫描")
            bluetoothManager?.startScanning()
            bluetoothButton.setTitle("⏹ 停止扫描", for: .normal)
            bluetoothButton.backgroundColor = .systemRed
        }
    }

    // 返回按钮点击事件
    @objc private func backButtonTapped(_ sender: UIButton) {
        print("← 返回发送端界面")

        // 先断开所有连接
        disconnect()
        
        // 返回上一个界面
        dismiss(animated: true)
    }
    
    // MARK: - Connection Management
    
    private func connect() {
        guard let ipAddress = ipTextField.text, !ipAddress.isEmpty else {
            showAlert(title: "错误", message: "请输入服务器IP地址")
            return
        }
        
        print("🔌 开始连接到服务器: \(ipAddress)")
        statusLabel.text = "连接中..."
        connectButton.isEnabled = false
        
        networkManager?.connectToServer(host: ipAddress) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success:
                    print("✅ 网络连接成功")
                    self?.onConnected()
                case .failure(let error):
                    print("❌ 网络连接失败: \(error)")
                    self?.onConnectionFailed(error)
                }
            }
        }
    }
    
    private func disconnect() {
        // 停止解码器
        videoDecoder?.stopDecoding()
        audioDecoder?.stopDecoding()
        audioPlayer?.stop()
        
        // 断开网络连接
        networkManager?.stopServer()
        
        // 停止蓝牙
        bluetoothManager?.stopScanning()
        bluetoothManager?.disconnect()
        
        // 清空显示 - 修复显示层清空问题
        displayLayer?.flushAndRemoveImage()
        
        // 更新UI
        isConnected = false
        updateUI()
    }
    
    private func onConnected() {
        isConnected = true
        print("🎯 连接成功，开始初始化解码器...")
        
        // 启动解码器
        do {
            print("📹 启动视频解码器...")
            try videoDecoder?.startDecoding()
            print("🎵 启动音频解码器...")
            try audioDecoder?.startDecoding()
            print("🔊 启动音频播放器...")
            try audioPlayer?.start()
            print("✅ 所有解码器启动成功")
        } catch {
            print("❌ 启动解码器失败: \(error)")
            showAlert(title: "错误", message: "启动解码器失败: \(error.localizedDescription)")
            disconnect()
            return
        }
        
        updateUI()
    }
    
    private func onConnectionFailed(_ error: Error) {
        showAlert(title: "连接失败", message: error.localizedDescription)
        updateUI()
    }
    
    private func updateUI() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if self.isConnected {
                self.statusLabel.text = "已连接 - FPS: \(String(format: "%.1f", self.fps))"
                self.statusLabel.textColor = .systemGreen
                self.connectButton.setTitle("断开", for: .normal)
                self.connectButton.backgroundColor = .systemRed
            } else {
                self.statusLabel.text = "未连接"
                self.statusLabel.textColor = .label
                self.connectButton.setTitle("连接", for: .normal)
                self.connectButton.backgroundColor = .systemBlue
            }
            
            self.connectButton.isEnabled = true
        }
    }
    
    // MARK: - Video Display
    
    private func displayVideoFrame(_ pixelBuffer: CVPixelBuffer, timestamp: UInt64) {
        guard let layer = displayLayer else { return }
        
        // 创建CMSampleBuffer用于显示
        var formatDescription: CMVideoFormatDescription?
        let status1 = CMVideoFormatDescriptionCreateForImageBuffer(
            allocator: kCFAllocatorDefault,
            imageBuffer: pixelBuffer,
            formatDescriptionOut: &formatDescription
        )
        
        guard status1 == noErr, let formatDesc = formatDescription else {
            print("Failed to create format description: \(status1)")
            return
        }
        
        var sampleBuffer: CMSampleBuffer?
        let presentationTime = CMTime(value: Int64(timestamp), timescale: 1000)
        
        var sampleTimingInfo = CMSampleTimingInfo(
            duration: CMTime.invalid,
            presentationTimeStamp: presentationTime,
            decodeTimeStamp: CMTime.invalid
        )
        let status2 = CMSampleBufferCreateReadyWithImageBuffer(
            allocator: kCFAllocatorDefault,
            imageBuffer: pixelBuffer,
            formatDescription: formatDesc,
            sampleTiming: &sampleTimingInfo,
            sampleBufferOut: &sampleBuffer
        )
        
        guard status2 == noErr, let sample = sampleBuffer else {
            print("Failed to create sample buffer: \(status2)")
            return
        }
        
        // 在主线程显示
        DispatchQueue.main.async {
            if layer.isReadyForMoreMediaData {
                layer.enqueue(sample)
                self.updateFrameRate()
            }
        }
    }
    
    private func updateFrameRate() {
        frameCount += 1
        let currentTime = CACurrentMediaTime()
        
        if lastFrameTime == 0 {
            lastFrameTime = currentTime
        } else if currentTime - lastFrameTime >= 1.0 {
            fps = Double(frameCount) / (currentTime - lastFrameTime)
            frameCount = 0
            lastFrameTime = currentTime
            
            // 更新UI
            updateUI()
        }
    }
    
    // MARK: - Utility
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - NetworkManagerDelegate

extension PlayerViewController: NetworkManagerDelegate {
    func networkManager(_ manager: NetworkManager, didReceiveVideoFrame data: Data, timestamp: UInt64) {
        print("📹 接收到视频帧: \(data.count) 字节, 时间戳: \(timestamp)")
        videoDecoder?.decodeFrame(data, timestamp: timestamp)
    }

    func networkManager(_ manager: NetworkManager, didReceiveAudioFrame data: Data, timestamp: UInt64) {
        print("🎵 接收到音频帧: \(data.count) 字节, 时间戳: \(timestamp)")
        audioDecoder?.decodeAudioFrame(data, timestamp: timestamp)
    }

    func networkManager(_ manager: NetworkManager, didReceiveControlCommand data: Data) {
        guard let command = String(data: data, encoding: .utf8) else { return }

        DispatchQueue.main.async { [weak self] in
            switch command {
            case "streaming":
                self?.statusLabel.text = "正在接收投屏"
            case "idle":
                self?.statusLabel.text = "发送端空闲"
            default:
                print("Unknown control command: \(command)")
            }
        }
    }

    func networkManager(_ manager: NetworkManager, clientDidConnect client: String) {
        print("✅ 服务器客户端连接: \(client)")
    }

    func networkManager(_ manager: NetworkManager, clientDidDisconnect client: String) {
        print("❌ 服务器客户端断开: \(client)")
    }

    func networkManager(_ manager: NetworkManager, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "网络错误", message: error.localizedDescription)
            self?.disconnect()
        }
    }
}

// MARK: - BluetoothManagerDelegate

extension PlayerViewController: BluetoothManagerDelegate {
    func bluetoothManager(_ manager: BluetoothManager, didReceiveCommand command: ControlCommand) {
        DispatchQueue.main.async { [weak self] in
            switch command {
            case .startMirroring:
                self?.statusLabel.text = "开始投屏"
            case .stopMirroring:
                self?.statusLabel.text = "停止投屏"
            case .pauseMirroring:
                self?.statusLabel.text = "暂停投屏"
            case .resumeMirroring:
                self?.statusLabel.text = "恢复投屏"
            default:
                break
            }
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didUpdateStatus status: MirroringStatus) {
        DispatchQueue.main.async { [weak self] in
            switch status {
            case .idle:
                self?.qualityLabel.text = "状态: 空闲"
            case .connecting:
                self?.qualityLabel.text = "状态: 连接中"
            case .connected:
                self?.qualityLabel.text = "状态: 已连接"
            case .mirroring:
                self?.qualityLabel.text = "状态: 投屏中"
            case .paused:
                self?.qualityLabel.text = "状态: 已暂停"
            case .error:
                self?.qualityLabel.text = "状态: 错误"
            }
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didConnectToDevice device: CBPeripheral) {
        DispatchQueue.main.async { [weak self] in
            self?.bluetoothButton.setTitle("已连接", for: .normal)
            self?.bluetoothButton.backgroundColor = .systemGray
            self?.bluetoothButton.isEnabled = false
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didDisconnectFromDevice device: CBPeripheral) {
        DispatchQueue.main.async { [weak self] in
            self?.bluetoothButton.setTitle("蓝牙连接", for: .normal)
            self?.bluetoothButton.backgroundColor = .systemGreen
            self?.bluetoothButton.isEnabled = true
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "蓝牙错误", message: error.localizedDescription)
        }
    }
}

// MARK: - VideoDecoderDelegate

extension PlayerViewController: VideoDecoderDelegate {
    func videoDecoder(_ decoder: VideoDecoder, didDecodeFrame pixelBuffer: CVPixelBuffer, timestamp: UInt64) {
        print("🎬 视频解码成功，准备显示帧，时间戳: \(timestamp)")
        displayVideoFrame(pixelBuffer, timestamp: timestamp)
    }

    func videoDecoder(_ decoder: VideoDecoder, didEncounterError error: Error) {
        print("❌ 视频解码错误: \(error.localizedDescription)")
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "视频解码错误", message: error.localizedDescription)
        }
    }
}

// MARK: - AudioDecoderDelegate

extension PlayerViewController: AudioDecoderDelegate {
    func audioDecoder(_ decoder: AudioDecoder, didDecodeFrame data: Data, timestamp: UInt64) {
        audioPlayer?.playAudioData(data)
    }

    func audioDecoder(_ decoder: AudioDecoder, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "音频解码错误", message: error.localizedDescription)
        }
    }
}
