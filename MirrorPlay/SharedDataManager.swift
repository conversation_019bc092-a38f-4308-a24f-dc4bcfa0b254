//
//  SharedDataManager.swift
//  MirrorPlay - 主应用与扩展间的数据共享管理器
//
//  这个文件负责主应用和ReplayKit扩展之间的数据共享
//  使用UserDefaults和通知机制实现进程间通信
//

import Foundation

// MARK: - 共享数据管理器
class SharedDataManager {
    
    // 单例实例
    static let shared = SharedDataManager()
    
    // App Group 标识符（如果配置了App Groups的话）
    private let appGroupIdentifier = "group.com.test.MirrorPlay.shared"
    
    // 共享的UserDefaults
    private var sharedDefaults: UserDefaults? {
        return UserDefaults(suiteName: appGroupIdentifier)
    }
    
    // 标准UserDefaults作为备选
    private var defaults: UserDefaults {
        return sharedDefaults ?? UserDefaults.standard
    }
    
    private init() {
        print("📊 共享数据管理器初始化")
    }
    
    // MARK: - 服务器信息共享
    
    // 保存服务器端口信息
    func saveServerPort(_ port: UInt16) {
        defaults.set(port, forKey: "MirrorPlay.ServerPort")
        defaults.synchronize()
        print("💾 保存服务器端口: \(port)")
    }
    
    // 获取服务器端口信息
    func getServerPort() -> UInt16? {
        let port = defaults.object(forKey: "MirrorPlay.ServerPort") as? UInt16
        print("📖 读取服务器端口: \(port ?? 0)")
        return port
    }
    
    // 保存服务器IP地址
    func saveServerIP(_ ip: String) {
        defaults.set(ip, forKey: "MirrorPlay.ServerIP")
        defaults.synchronize()
        print("💾 保存服务器IP: \(ip)")
    }
    
    // 获取服务器IP地址
    func getServerIP() -> String? {
        let ip = defaults.string(forKey: "MirrorPlay.ServerIP")
        print("📖 读取服务器IP: \(ip ?? "未设置")")
        return ip
    }
    
    // MARK: - 推流状态共享
    
    // 保存推流状态
    func saveStreamingStatus(_ isStreaming: Bool) {
        defaults.set(isStreaming, forKey: "MirrorPlay.IsStreaming")
        defaults.synchronize()
        print("💾 保存推流状态: \(isStreaming)")
        
        // 发送通知
        NotificationCenter.default.post(
            name: NSNotification.Name("MirrorPlay.StreamingStatusChanged"),
            object: nil,
            userInfo: ["isStreaming": isStreaming]
        )
    }
    
    // 获取推流状态
    func getStreamingStatus() -> Bool {
        let status = defaults.bool(forKey: "MirrorPlay.IsStreaming")
        print("📖 读取推流状态: \(status)")
        return status
    }
    
    // MARK: - 连接信息共享
    
    // 保存已连接客户端数量
    func saveConnectedClientsCount(_ count: Int) {
        defaults.set(count, forKey: "MirrorPlay.ConnectedClients")
        defaults.synchronize()
        print("💾 保存连接客户端数: \(count)")
    }
    
    // 获取已连接客户端数量
    func getConnectedClientsCount() -> Int {
        let count = defaults.integer(forKey: "MirrorPlay.ConnectedClients")
        print("📖 读取连接客户端数: \(count)")
        return count
    }
    
    // MARK: - 编码设置共享
    
    // 保存视频质量设置
    func saveVideoQuality(_ quality: VideoQuality) {
        defaults.set(quality.rawValue, forKey: "MirrorPlay.VideoQuality")
        defaults.synchronize()
        print("💾 保存视频质量: \(quality)")
    }
    
    // 获取视频质量设置
    func getVideoQuality() -> VideoQuality {
        let rawValue = defaults.integer(forKey: "MirrorPlay.VideoQuality")
        let quality = VideoQuality(rawValue: rawValue) ?? .medium
        print("📖 读取视频质量: \(quality)")
        return quality
    }
    
    // MARK: - 错误信息共享
    
    // 保存最后的错误信息
    func saveLastError(_ error: String) {
        defaults.set(error, forKey: "MirrorPlay.LastError")
        defaults.set(Date(), forKey: "MirrorPlay.LastErrorTime")
        defaults.synchronize()
        print("💾 保存错误信息: \(error)")
    }
    
    // 获取最后的错误信息
    func getLastError() -> (error: String?, time: Date?) {
        let error = defaults.string(forKey: "MirrorPlay.LastError")
        let time = defaults.object(forKey: "MirrorPlay.LastErrorTime") as? Date
        print("📖 读取错误信息: \(error ?? "无"), 时间: \(time?.description ?? "无")")
        return (error, time)
    }
    
    // 清除错误信息
    func clearLastError() {
        defaults.removeObject(forKey: "MirrorPlay.LastError")
        defaults.removeObject(forKey: "MirrorPlay.LastErrorTime")
        defaults.synchronize()
        print("🧹 清除错误信息")
    }
    
    // MARK: - 统计信息共享
    
    // 保存帧率统计
    func saveFrameRate(_ fps: Double) {
        defaults.set(fps, forKey: "MirrorPlay.FrameRate")
        defaults.synchronize()
    }
    
    // 获取帧率统计
    func getFrameRate() -> Double {
        return defaults.double(forKey: "MirrorPlay.FrameRate")
    }
    
    // 保存数据传输统计
    func saveDataTransferStats(sent: Int64, received: Int64) {
        defaults.set(sent, forKey: "MirrorPlay.DataSent")
        defaults.set(received, forKey: "MirrorPlay.DataReceived")
        defaults.synchronize()
    }
    
    // 获取数据传输统计
    func getDataTransferStats() -> (sent: Int64, received: Int64) {
        let sent = defaults.object(forKey: "MirrorPlay.DataSent") as? Int64 ?? 0
        let received = defaults.object(forKey: "MirrorPlay.DataReceived") as? Int64 ?? 0
        return (sent, received)
    }
}

// MARK: - 视频质量枚举
enum VideoQuality: Int, CaseIterable {
    case low = 0
    case medium = 1
    case high = 2
    
    var title: String {
        switch self {
        case .low: return "低质量"
        case .medium: return "中等质量"
        case .high: return "高质量"
        }
    }
    
    var bitrate: Int32 {
        switch self {
        case .low: return 1000000    // 1 Mbps
        case .medium: return 2000000 // 2 Mbps
        case .high: return 4000000   // 4 Mbps
        }
    }
    
    var resolution: (width: Int32, height: Int32) {
        switch self {
        case .low: return (640, 480)
        case .medium: return (1280, 720)
        case .high: return (1920, 1080)
        }
    }
}

// MARK: - 通知名称扩展
extension NSNotification.Name {
    static let mirrorPlayStreamingStatusChanged = NSNotification.Name("MirrorPlay.StreamingStatusChanged")
    static let mirrorPlayClientConnected = NSNotification.Name("MirrorPlay.ClientConnected")
    static let mirrorPlayClientDisconnected = NSNotification.Name("MirrorPlay.ClientDisconnected")
    static let mirrorPlayErrorOccurred = NSNotification.Name("MirrorPlay.ErrorOccurred")
}
