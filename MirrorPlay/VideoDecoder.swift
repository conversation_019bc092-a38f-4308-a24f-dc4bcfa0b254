//
//  VideoDecoder.swift
//  MirrorPlay
//
//  Created by cf110 on 2025/7/24.
//

import Foundation
import VideoToolbox
import CoreMedia
import CoreVideo

protocol VideoDecoderDelegate: AnyObject {
    func videoDecoder(_ decoder: VideoDecoder, didDecodeFrame pixelBuffer: CVPixelBuffer, timestamp: UInt64)
    func videoDecoder(_ decoder: VideoDecoder, didEncounterError error: Error)
}

class VideoDecoder {
    weak var delegate: VideoDecoderDelegate?
    
    private var decompressionSession: VTDecompressionSession?
    private let decodingQueue = DispatchQueue(label: "VideoDecoder", qos: .userInitiated)
    
    // 格式描述符
    private var formatDescription: CMVideoFormatDescription?
    
    // SPS/PPS 缓存
    private var spsData: Data?
    private var ppsData: Data?
    
    init() {}
    
    deinit {
        stopDecoding()
    }
    
    func startDecoding() throws {
        // 解码器会在收到第一个关键帧时自动创建
        print("Video decoder ready")
    }
    
    func stopDecoding() {
        if let session = decompressionSession {
            VTDecompressionSessionInvalidate(session)
            decompressionSession = nil
        }
        
        formatDescription = nil
        spsData = nil
        ppsData = nil
        
        print("Video decoder stopped")
    }
    
    func decodeFrame(_ data: Data, timestamp: UInt64) {
        decodingQueue.async { [weak self] in
            self?.processFrameData(data, timestamp: timestamp)
        }
    }
    
    private func processFrameData(_ data: Data, timestamp: UInt64) {
        // 解析Annex-B格式的NALU
        let nalus = parseAnnexBData(data)
        
        for nalu in nalus {
            processNALU(nalu, timestamp: timestamp)
        }
    }
    
    private func parseAnnexBData(_ data: Data) -> [Data] {
        var nalus: [Data] = []
        var offset = 0
        
        while offset < data.count {
            // 查找start code (0x00000001 或 0x000001)
            var startCodeLength = 0
            
            if offset + 4 <= data.count {
                let fourBytes = data.subdata(in: offset..<offset+4)
                if fourBytes == Data([0x00, 0x00, 0x00, 0x01]) {
                    startCodeLength = 4
                }
            }
            
            if startCodeLength == 0 && offset + 3 <= data.count {
                let threeBytes = data.subdata(in: offset..<offset+3)
                if threeBytes == Data([0x00, 0x00, 0x01]) {
                    startCodeLength = 3
                }
            }
            
            if startCodeLength == 0 {
                offset += 1
                continue
            }
            
            // 找到下一个start code
            var nextOffset = offset + startCodeLength
            var nextStartCodeLength = 0
            
            while nextOffset < data.count {
                if nextOffset + 4 <= data.count {
                    let fourBytes = data.subdata(in: nextOffset..<nextOffset+4)
                    if fourBytes == Data([0x00, 0x00, 0x00, 0x01]) {
                        nextStartCodeLength = 4
                        break
                    }
                }
                
                if nextOffset + 3 <= data.count {
                    let threeBytes = data.subdata(in: nextOffset..<nextOffset+3)
                    if threeBytes == Data([0x00, 0x00, 0x01]) {
                        nextStartCodeLength = 3
                        break
                    }
                }
                
                nextOffset += 1
            }
            
            // 提取NALU数据
            let naluStart = offset + startCodeLength
            let naluEnd = nextStartCodeLength > 0 ? nextOffset : data.count
            
            if naluStart < naluEnd {
                let naluData = data.subdata(in: naluStart..<naluEnd)
                nalus.append(naluData)
            }
            
            offset = nextOffset
        }
        
        return nalus
    }
    
    private func processNALU(_ nalu: Data, timestamp: UInt64) {
        guard !nalu.isEmpty else { return }
        
        let naluType = nalu[0] & 0x1F
        
        switch naluType {
        case 7: // SPS
            spsData = nalu
            print("Received SPS: \(nalu.count) bytes")
            
        case 8: // PPS
            ppsData = nalu
            print("Received PPS: \(nalu.count) bytes")
            
            // 当收到PPS后，尝试创建格式描述符
            if let sps = spsData {
                createFormatDescription(sps: sps, pps: nalu)
            }
            
        case 1, 5: // 非IDR帧或IDR帧
            if decompressionSession == nil {
                createDecompressionSession()
            }
            
            decodeVideoFrame(nalu, timestamp: timestamp, isKeyFrame: naluType == 5)
            
        default:
            print("Unknown NALU type: \(naluType)")
        }
    }
    
    private func createFormatDescription(sps: Data, pps: Data) {
        let parameterSets = [sps, pps]
        let parameterSetPointers = parameterSets.map { $0.withUnsafeBytes { $0.bindMemory(to: UInt8.self).baseAddress! } }
        let parameterSetSizes = parameterSets.map { $0.count }
        
        let status = CMVideoFormatDescriptionCreateFromH264ParameterSets(
            allocator: kCFAllocatorDefault,
            parameterSetCount: parameterSets.count,
            parameterSetPointers: parameterSetPointers,
            parameterSetSizes: parameterSetSizes,
            nalUnitHeaderLength: 4,
            formatDescriptionOut: &formatDescription
        )
        
        if status == noErr {
            print("Format description created successfully")
        } else {
            print("Failed to create format description: \(status)")
        }
    }
    
    private func createDecompressionSession() {
        guard let formatDesc = formatDescription else {
            print("Format description not available")
            return
        }
        
        let callback: VTDecompressionOutputCallback = { (decompressionOutputRefCon, sourceFrameRefCon, status, infoFlags, imageBuffer, presentationTimeStamp, presentationDuration) in
            
            guard let imageBuffer = imageBuffer else { return }
            
            let decoder = Unmanaged<VideoDecoder>.fromOpaque(decompressionOutputRefCon!).takeUnretainedValue()
            let timestamp = UInt64(CMTimeGetSeconds(presentationTimeStamp) * 1000)
            
            decoder.delegate?.videoDecoder(decoder, didDecodeFrame: imageBuffer, timestamp: timestamp)
        }
        
        let attributes: [CFString: Any] = [
            kCVPixelBufferPixelFormatTypeKey: kCVPixelFormatType_420YpCbCr8BiPlanarVideoRange
        ]
        
        let status = VTDecompressionSessionCreate(
            allocator: kCFAllocatorDefault,
            formatDescription: formatDesc,
            decoderSpecification: nil,
            imageBufferAttributes: attributes as CFDictionary,
            outputCallback: callback,
            refcon: Unmanaged.passUnretained(self).toOpaque(),
            decompressionSessionOut: &decompressionSession
        )
        
        if status == noErr {
            print("Decompression session created successfully")
        } else {
            print("Failed to create decompression session: \(status)")
            delegate?.videoDecoder(self, didEncounterError: VideoDecoderError.sessionCreationFailed(status))
        }
    }
    
    private func decodeVideoFrame(_ nalu: Data, timestamp: UInt64, isKeyFrame: Bool) {
        guard let session = decompressionSession else {
            print("Decompression session not ready")
            return
        }
        
        // 转换为AVCC格式（长度前缀）
        let avccData = convertToAVCC(nalu)
        
        // 创建CMBlockBuffer
        var blockBuffer: CMBlockBuffer?
        let status1 = CMBlockBufferCreateWithMemoryBlock(
            allocator: kCFAllocatorDefault,
            memoryBlock: nil,
            blockLength: avccData.count,
            blockAllocator: kCFAllocatorDefault,
            customBlockSource: nil,
            offsetToData: 0,
            dataLength: avccData.count,
            flags: 0,
            blockBufferOut: &blockBuffer
        )
        
        guard status1 == noErr, let buffer = blockBuffer else {
            delegate?.videoDecoder(self, didEncounterError: VideoDecoderError.blockBufferCreationFailed(status1))
            return
        }
        
        // 复制数据到CMBlockBuffer
        let status2 = CMBlockBufferReplaceDataBytes(
            with: avccData.withUnsafeBytes { $0.bindMemory(to: UInt8.self).baseAddress! },
            blockBuffer: buffer,
            offsetIntoDestination: 0,
            dataLength: avccData.count
        )
        
        guard status2 == noErr else {
            delegate?.videoDecoder(self, didEncounterError: VideoDecoderError.dataCopyFailed(status2))
            return
        }
        
        // 创建CMSampleBuffer
        var sampleBuffer: CMSampleBuffer?
        let presentationTime = CMTime(value: Int64(timestamp), timescale: 1000)
        
        let status3 = CMSampleBufferCreateReady(
            allocator: kCFAllocatorDefault,
            dataBuffer: buffer,
            formatDescription: formatDescription,
            sampleCount: 1,
            sampleTimingEntryCount: 1,
            sampleTimingArray: &CMSampleTimingInfo(
                duration: CMTime.invalid,
                presentationTimeStamp: presentationTime,
                decodeTimeStamp: CMTime.invalid
            ),
            sampleSizeEntryCount: 1,
            sampleSizeArray: &avccData.count,
            sampleBufferOut: &sampleBuffer
        )
        
        guard status3 == noErr, let sample = sampleBuffer else {
            delegate?.videoDecoder(self, didEncounterError: VideoDecoderError.sampleBufferCreationFailed(status3))
            return
        }
        
        // 解码帧
        let status4 = VTDecompressionSessionDecodeFrame(
            session,
            sampleBuffer: sample,
            flags: [],
            frameRefcon: nil,
            infoFlagsOut: nil
        )
        
        if status4 != noErr {
            delegate?.videoDecoder(self, didEncounterError: VideoDecoderError.decodingFailed(status4))
        }
    }
    
    private func convertToAVCC(_ nalu: Data) -> Data {
        var avccData = Data()
        
        // 添加长度前缀（4字节，大端序）
        let length = UInt32(nalu.count).bigEndian
        avccData.append(Data(bytes: &length, count: 4))
        
        // 添加NALU数据
        avccData.append(nalu)
        
        return avccData
    }
}

// MARK: - 错误定义

enum VideoDecoderError: Error, LocalizedError {
    case sessionCreationFailed(OSStatus)
    case blockBufferCreationFailed(OSStatus)
    case sampleBufferCreationFailed(OSStatus)
    case dataCopyFailed(OSStatus)
    case decodingFailed(OSStatus)
    
    var errorDescription: String? {
        switch self {
        case .sessionCreationFailed(let status):
            return "Failed to create decompression session: \(status)"
        case .blockBufferCreationFailed(let status):
            return "Failed to create block buffer: \(status)"
        case .sampleBufferCreationFailed(let status):
            return "Failed to create sample buffer: \(status)"
        case .dataCopyFailed(let status):
            return "Failed to copy data: \(status)"
        case .decodingFailed(let status):
            return "Video decoding failed: \(status)"
        }
    }
}
