//
//  VideoEncoder.swift
//  MirrorPlay
//
//  Created by cf110 on 2025/7/24.
//

import Foundation
import VideoToolbox
import CoreMedia
import CoreVideo

protocol VideoEncoderDelegate: AnyObject {
    func videoEncoder(_ encoder: VideoEncoder, didEncodeFrame data: Data, timestamp: UInt64, isKeyFrame: Bool)
    func videoEncoder(_ encoder: VideoEncoder, didEncounterError error: Error)
}

class VideoEncoder {
    weak var delegate: VideoEncoderDelegate?
    
    private var compressionSession: VTCompressionSession?
    private let encodingQueue = DispatchQueue(label: "VideoEncoder", qos: .userInitiated)
    
    // 编码参数
    private let width: Int32
    private let height: Int32
    private let bitrate: Int32
    private let fps: Int32
    
    // 帧计数器
    private var frameCount: Int64 = 0
    private var keyFrameInterval: Int32 = 30 // 每30帧一个关键帧
    
    init(width: Int32, height: Int32, bitrate: Int32 = 2000000, fps: Int32 = 30) {
        self.width = width
        self.height = height
        self.bitrate = bitrate
        self.fps = fps
    }
    
    deinit {
        stopEncoding()
    }
    
    func startEncoding() throws {
        guard compressionSession == nil else { return }
        
        let callback: VTCompressionOutputCallback = { (outputCallbackRefCon, sourceFrameRefCon, status, infoFlags, sampleBuffer) in
            guard let sampleBuffer = sampleBuffer else { return }
            
            let encoder = Unmanaged<VideoEncoder>.fromOpaque(outputCallbackRefCon!).takeUnretainedValue()
            encoder.handleEncodedFrame(sampleBuffer: sampleBuffer, status: status)
        }
        
        let status = VTCompressionSessionCreate(
            allocator: kCFAllocatorDefault,
            width: width,
            height: height,
            codecType: kCMVideoCodecType_H264,
            encoderSpecification: nil,
            imageBufferAttributes: nil,
            compressedDataAllocator: kCFAllocatorDefault,
            outputCallback: callback,
            refcon: Unmanaged.passUnretained(self).toOpaque(),
            compressionSessionOut: &compressionSession
        )
        
        guard status == noErr, let session = compressionSession else {
            throw VideoEncoderError.sessionCreationFailed(status)
        }
        
        // 配置编码参数
        try configureSession(session)
        
        // 准备编码
        VTCompressionSessionPrepareToEncodeFrames(session)
        
        print("Video encoder started: \(width)x\(height) @ \(bitrate) bps, \(fps) fps")
    }
    
    func stopEncoding() {
        if let session = compressionSession {
            VTCompressionSessionCompleteFrames(session, untilPresentationTimeStamp: CMTime.invalid)
            VTCompressionSessionInvalidate(session)
            compressionSession = nil
        }
        frameCount = 0
        print("Video encoder stopped")
    }
    
    private func configureSession(_ session: VTCompressionSession) throws {
        // 设置实时编码
        var status = VTSessionSetProperty(session, kVTCompressionPropertyKey_RealTime, kCFBooleanTrue)
        guard status == noErr else { throw VideoEncoderError.configurationFailed("RealTime", status) }
        
        // 设置码率
        let bitrateNumber = CFNumberCreate(kCFAllocatorDefault, .sInt32Type, &bitrate)
        status = VTSessionSetProperty(session, kVTCompressionPropertyKey_AverageBitRate, bitrateNumber)
        guard status == noErr else { throw VideoEncoderError.configurationFailed("AverageBitRate", status) }
        
        // 设置最大关键帧间隔
        let keyFrameIntervalNumber = CFNumberCreate(kCFAllocatorDefault, .sInt32Type, &keyFrameInterval)
        status = VTSessionSetProperty(session, kVTCompressionPropertyKey_MaxKeyFrameInterval, keyFrameIntervalNumber)
        guard status == noErr else { throw VideoEncoderError.configurationFailed("MaxKeyFrameInterval", status) }
        
        // 设置帧率
        let fpsNumber = CFNumberCreate(kCFAllocatorDefault, .sInt32Type, &fps)
        status = VTSessionSetProperty(session, kVTCompressionPropertyKey_ExpectedFrameRate, fpsNumber)
        guard status == noErr else { throw VideoEncoderError.configurationFailed("ExpectedFrameRate", status) }
        
        // 设置Profile Level
        status = VTSessionSetProperty(session, kVTCompressionPropertyKey_ProfileLevel, kVTProfileLevel_H264_Baseline_AutoLevel)
        guard status == noErr else { throw VideoEncoderError.configurationFailed("ProfileLevel", status) }
        
        // 设置熵编码模式
        status = VTSessionSetProperty(session, kVTCompressionPropertyKey_H264EntropyMode, kVTH264EntropyMode_CAVLC)
        guard status == noErr else { throw VideoEncoderError.configurationFailed("H264EntropyMode", status) }
        
        // 允许帧重排序
        status = VTSessionSetProperty(session, kVTCompressionPropertyKey_AllowFrameReordering, kCFBooleanFalse)
        guard status == noErr else { throw VideoEncoderError.configurationFailed("AllowFrameReordering", status) }
    }
    
    func encodeFrame(_ pixelBuffer: CVPixelBuffer, timestamp: CMTime) {
        guard let session = compressionSession else {
            delegate?.videoEncoder(self, didEncounterError: VideoEncoderError.sessionNotReady)
            return
        }
        
        encodingQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 检查是否需要强制关键帧
            var frameProperties: CFDictionary?
            if self.frameCount % Int64(self.keyFrameInterval) == 0 {
                let keyFrameDict = [kVTEncodeFrameOptionKey_ForceKeyFrame: kCFBooleanTrue]
                frameProperties = keyFrameDict as CFDictionary
            }
            
            let status = VTCompressionSessionEncodeFrame(
                session,
                imageBuffer: pixelBuffer,
                presentationTimeStamp: timestamp,
                duration: CMTime.invalid,
                frameProperties: frameProperties,
                sourceFrameRefcon: nil,
                infoFlagsOut: nil
            )
            
            if status != noErr {
                self.delegate?.videoEncoder(self, didEncounterError: VideoEncoderError.encodingFailed(status))
            }
            
            self.frameCount += 1
        }
    }
    
    private func handleEncodedFrame(sampleBuffer: CMSampleBuffer, status: OSStatus) {
        guard status == noErr else {
            delegate?.videoEncoder(self, didEncounterError: VideoEncoderError.encodingFailed(status))
            return
        }
        
        guard let dataBuffer = CMSampleBufferGetDataBuffer(sampleBuffer) else {
            delegate?.videoEncoder(self, didEncounterError: VideoEncoderError.invalidSampleBuffer)
            return
        }
        
        // 获取编码数据
        let dataLength = CMBlockBufferGetDataLength(dataBuffer)
        var data = Data(count: dataLength)
        
        let copyStatus = data.withUnsafeMutableBytes { bytes in
            CMBlockBufferCopyDataBytes(dataBuffer, atOffset: 0, dataLength: dataLength, destination: bytes.bindMemory(to: UInt8.self).baseAddress!)
        }
        
        guard copyStatus == noErr else {
            delegate?.videoEncoder(self, didEncounterError: VideoEncoderError.dataCopyFailed(copyStatus))
            return
        }
        
        // 检查是否为关键帧
        let isKeyFrame = isKeyFrameSampleBuffer(sampleBuffer)
        
        // 获取时间戳
        let presentationTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
        let timestamp = UInt64(CMTimeGetSeconds(presentationTime) * 1000) // 转换为毫秒
        
        // 转换为Annex-B格式（添加start codes）
        let annexBData = convertToAnnexB(data)
        
        delegate?.videoEncoder(self, didEncodeFrame: annexBData, timestamp: timestamp, isKeyFrame: isKeyFrame)
    }
    
    private func isKeyFrameSampleBuffer(_ sampleBuffer: CMSampleBuffer) -> Bool {
        guard let attachments = CMSampleBufferGetSampleAttachmentsArray(sampleBuffer, createIfNecessary: false) as? [[CFString: Any]],
              let attachment = attachments.first else {
            return false
        }
        
        return attachment[kCMSampleAttachmentKey_NotSync] == nil
    }
    
    private func convertToAnnexB(_ data: Data) -> Data {
        var annexBData = Data()
        var offset = 0
        
        while offset < data.count {
            // 读取NALU长度（前4字节）
            guard offset + 4 <= data.count else { break }
            
            let naluLength = data.subdata(in: offset..<offset+4).withUnsafeBytes { bytes in
                bytes.load(as: UInt32.self).bigEndian
            }
            
            offset += 4
            
            guard offset + Int(naluLength) <= data.count else { break }
            
            // 添加start code (0x00000001)
            annexBData.append(contentsOf: [0x00, 0x00, 0x00, 0x01])
            
            // 添加NALU数据
            annexBData.append(data.subdata(in: offset..<offset+Int(naluLength)))
            
            offset += Int(naluLength)
        }
        
        return annexBData
    }
}

// MARK: - 错误定义

enum VideoEncoderError: Error, LocalizedError {
    case sessionCreationFailed(OSStatus)
    case configurationFailed(String, OSStatus)
    case sessionNotReady
    case encodingFailed(OSStatus)
    case invalidSampleBuffer
    case dataCopyFailed(OSStatus)
    
    var errorDescription: String? {
        switch self {
        case .sessionCreationFailed(let status):
            return "Failed to create compression session: \(status)"
        case .configurationFailed(let property, let status):
            return "Failed to configure \(property): \(status)"
        case .sessionNotReady:
            return "Compression session not ready"
        case .encodingFailed(let status):
            return "Encoding failed: \(status)"
        case .invalidSampleBuffer:
            return "Invalid sample buffer"
        case .dataCopyFailed(let status):
            return "Failed to copy data: \(status)"
        }
    }
}

// MARK: - 工具扩展

extension VideoEncoder {
    // 从CMSampleBuffer创建CVPixelBuffer（用于ReplayKit集成）
    static func pixelBuffer(from sampleBuffer: CMSampleBuffer) -> CVPixelBuffer? {
        return CMSampleBufferGetImageBuffer(sampleBuffer)
    }
    
    // 获取推荐的编码参数
    static func recommendedSettings(for size: CGSize) -> (width: Int32, height: Int32, bitrate: Int32) {
        let width = Int32(size.width)
        let height = Int32(size.height)
        
        // 根据分辨率计算推荐码率
        let pixels = width * height
        let bitrate: Int32
        
        switch pixels {
        case 0..<(640*480):
            bitrate = 1000000 // 1 Mbps
        case (640*480)..<(1280*720):
            bitrate = 2000000 // 2 Mbps
        case (1280*720)..<(1920*1080):
            bitrate = 4000000 // 4 Mbps
        default:
            bitrate = 6000000 // 6 Mbps
        }
        
        return (width, height, bitrate)
    }
}
