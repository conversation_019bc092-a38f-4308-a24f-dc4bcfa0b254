//
//  VideoEncoder.swift
//  MirrorPlay - 视频编码器
//
//  这个文件负责将ReplayKit捕获的原始视频帧编码为H.264格式
//  H.264是一种高效的视频压缩标准，可以大幅减少视频数据的大小
//
//  工作流程：
//  1. 接收ReplayKit提供的CVPixelBuffer（原始像素数据）
//  2. 使用iOS的VideoToolbox框架进行硬件加速编码
//  3. 输出压缩后的H.264数据流
//  4. 通过代理模式将编码结果传递给网络传输模块
//

import Foundation
import VideoToolbox  // 苹果的视频编解码框架
import CoreMedia     // 媒体数据处理框架
import CoreVideo     // 视频数据处理框架

// 视频编码器代理协议 - 用于通知编码结果
protocol VideoEncoderDelegate: AnyObject {
    // 编码完成一帧视频时调用
    func videoEncoder(_ encoder: VideoEncoder, didEncodeFrame data: Data, timestamp: UInt64, isKeyFrame: Bool)

    // 编码过程中出现错误时调用
    func videoEncoder(_ encoder: VideoEncoder, didEncounterError error: Error)
}

class VideoEncoder {
    // 代理对象 - 接收编码结果的对象
    weak var delegate: VideoEncoderDelegate?

    // 视频压缩会话 - iOS系统提供的硬件编码器
    private var compressionSession: VTCompressionSession?

    // 编码队列 - 在后台线程进行编码，避免阻塞主线程
    private let encodingQueue = DispatchQueue(label: "VideoEncoder", qos: .userInitiated)

    // MARK: - 编码参数
    // 这些参数决定了视频的质量和文件大小

    private let width: Int32      // 视频宽度（像素）
    private let height: Int32     // 视频高度（像素）
    private var bitrate: Int32    // 码率（每秒传输的比特数，越高质量越好但文件越大）
    private var fps: Int32        // 帧率（每秒帧数，通常30fps）

    // MARK: - 内部状态变量

    private var frameCount: Int64 = 0           // 已编码的帧数量
    private var keyFrameInterval: Int32 = 30    // 关键帧间隔（每30帧插入一个关键帧）

    // 关键帧（I帧）：包含完整图像信息，可以独立解码
    // 普通帧（P帧）：只包含与前一帧的差异信息，文件更小但需要依赖前面的帧

    // 初始化编码器
    init(width: Int32, height: Int32, bitrate: Int32 = 2000000, fps: Int32 = 30) {
        self.width = width
        self.height = height
        self.bitrate = bitrate  // 默认2Mbps码率
        self.fps = fps          // 默认30fps

        print("📹 创建视频编码器: \(width)x\(height), \(bitrate)bps, \(fps)fps")
    }

    // 析构函数 - 对象销毁时自动调用
    deinit {
        stopEncoding()  // 确保资源被正确释放
    }

    // 开始编码
    func startEncoding() throws {
        // 防止重复启动
        guard compressionSession == nil else {
            print("⚠️ 编码器已经启动")
            return
        }

        print("🚀 启动视频编码器...")

        // 定义编码完成回调函数
        // 这是一个C风格的回调函数，当编码器完成一帧编码时会调用
        let callback: VTCompressionOutputCallback = { (outputCallbackRefCon, sourceFrameRefCon, status, infoFlags, sampleBuffer) in
            // 检查编码是否成功
            guard let sampleBuffer = sampleBuffer else { return }

            // 从回调参数中恢复编码器对象
            let encoder = Unmanaged<VideoEncoder>.fromOpaque(outputCallbackRefCon!).takeUnretainedValue()

            // 处理编码结果
            encoder.handleEncodedFrame(sampleBuffer: sampleBuffer, status: status)
        }

        // 创建视频压缩会话
        let status = VTCompressionSessionCreate(
            allocator: kCFAllocatorDefault,           // 内存分配器
            width: width,                             // 视频宽度
            height: height,                           // 视频高度
            codecType: kCMVideoCodecType_H264,        // 编码格式：H.264
            encoderSpecification: nil,                // 编码器规格（nil表示使用默认）
            imageBufferAttributes: nil,               // 图像缓冲区属性
            compressedDataAllocator: kCFAllocatorDefault,  // 压缩数据分配器
            outputCallback: callback,                 // 编码完成回调
            refcon: Unmanaged.passUnretained(self).toOpaque(),  // 传递给回调的用户数据
            compressionSessionOut: &compressionSession  // 输出的压缩会话
        )

        // 检查创建是否成功
        guard status == noErr, let session = compressionSession else {
            print("❌ 创建压缩会话失败: \(status)")
            throw VideoEncoderError.sessionCreationFailed(status)
        }

        // 配置编码参数（码率、帧率等）
        try configureSession(session)

        // 准备开始编码
        VTCompressionSessionPrepareToEncodeFrames(session)

        print("✅ 视频编码器启动成功: \(width)x\(height) @ \(bitrate) bps, \(fps) fps")
    }
    
    func stopEncoding() {
        if let session = compressionSession {
            VTCompressionSessionCompleteFrames(session, untilPresentationTimeStamp: CMTime.invalid)
            VTCompressionSessionInvalidate(session)
            compressionSession = nil
        }
        frameCount = 0
        print("Video encoder stopped")
    }
    
    private func configureSession(_ session: VTCompressionSession) throws {
        // 设置实时编码
        var status = VTSessionSetProperty(
            session,
            key: kVTCompressionPropertyKey_RealTime,
            value: kCFBooleanTrue
        )
        guard status == noErr else { throw VideoEncoderError.configurationFailed("RealTime", status) }
        
        // 设置码率
        let bitrateNumber = CFNumberCreate(kCFAllocatorDefault, .sInt32Type, &bitrate)
        status = VTSessionSetProperty(
            session,
            key: kVTCompressionPropertyKey_AverageBitRate,
            value: bitrateNumber
        )
        guard status == noErr else { throw VideoEncoderError.configurationFailed("AverageBitRate", status) }
        
        // 设置最大关键帧间隔
        let keyFrameIntervalNumber = CFNumberCreate(kCFAllocatorDefault, .sInt32Type, &keyFrameInterval)
        status = VTSessionSetProperty(
            session,
            key: kVTCompressionPropertyKey_MaxKeyFrameInterval,
            value: keyFrameIntervalNumber
        )
        guard status == noErr else { throw VideoEncoderError.configurationFailed("MaxKeyFrameInterval", status) }
        
        // 设置帧率
        let fpsNumber = CFNumberCreate(kCFAllocatorDefault, .sInt32Type, &fps)
        status = VTSessionSetProperty(
            session,
            key: kVTCompressionPropertyKey_ExpectedFrameRate,
            value: fpsNumber
        )
        guard status == noErr else { throw VideoEncoderError.configurationFailed("ExpectedFrameRate", status) }
        
        // 设置Profile Level
        status = VTSessionSetProperty(
            session,
            key: kVTCompressionPropertyKey_ProfileLevel,
            value: kVTProfileLevel_H264_Baseline_AutoLevel
        )
        guard status == noErr else { throw VideoEncoderError.configurationFailed("ProfileLevel", status) }
        
        // 设置熵编码模式
        status = VTSessionSetProperty(
            session,
            key: kVTCompressionPropertyKey_H264EntropyMode,
            value: kVTH264EntropyMode_CAVLC
        )
        guard status == noErr else { throw VideoEncoderError.configurationFailed("H264EntropyMode", status) }
        
        // 允许帧重排序
        status = VTSessionSetProperty(
            session,
            key: kVTCompressionPropertyKey_AllowFrameReordering,
            value: kCFBooleanFalse
        )
        guard status == noErr else { throw VideoEncoderError.configurationFailed("AllowFrameReordering", status) }
    }
    
    func encodeFrame(_ pixelBuffer: CVPixelBuffer, timestamp: CMTime) {
        guard let session = compressionSession else {
            delegate?.videoEncoder(self, didEncounterError: VideoEncoderError.sessionNotReady)
            return
        }
        
        encodingQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 检查是否需要强制关键帧
            var frameProperties: CFDictionary?
            if self.frameCount % Int64(self.keyFrameInterval) == 0 {
                let keyFrameDict = [kVTEncodeFrameOptionKey_ForceKeyFrame: kCFBooleanTrue]
                frameProperties = keyFrameDict as CFDictionary
            }
            
            let status = VTCompressionSessionEncodeFrame(
                session,
                imageBuffer: pixelBuffer,
                presentationTimeStamp: timestamp,
                duration: CMTime.invalid,
                frameProperties: frameProperties,
                sourceFrameRefcon: nil,
                infoFlagsOut: nil
            )
            
            if status != noErr {
                self.delegate?.videoEncoder(self, didEncounterError: VideoEncoderError.encodingFailed(status))
            }
            
            self.frameCount += 1
        }
    }
    
    private func handleEncodedFrame(sampleBuffer: CMSampleBuffer, status: OSStatus) {
        guard status == noErr else {
            delegate?.videoEncoder(self, didEncounterError: VideoEncoderError.encodingFailed(status))
            return
        }
        
        guard let dataBuffer = CMSampleBufferGetDataBuffer(sampleBuffer) else {
            delegate?.videoEncoder(self, didEncounterError: VideoEncoderError.invalidSampleBuffer)
            return
        }
        
        // 获取编码数据
        let dataLength = CMBlockBufferGetDataLength(dataBuffer)
        var data = Data(count: dataLength)
        
        let copyStatus = data.withUnsafeMutableBytes { bytes in
            CMBlockBufferCopyDataBytes(dataBuffer, atOffset: 0, dataLength: dataLength, destination: bytes.bindMemory(to: UInt8.self).baseAddress!)
        }
        
        guard copyStatus == noErr else {
            delegate?.videoEncoder(self, didEncounterError: VideoEncoderError.dataCopyFailed(copyStatus))
            return
        }
        
        // 检查是否为关键帧
        let isKeyFrame = isKeyFrameSampleBuffer(sampleBuffer)
        
        // 获取时间戳
        let presentationTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
        let timestamp = UInt64(CMTimeGetSeconds(presentationTime) * 1000) // 转换为毫秒
        
        // 转换为Annex-B格式（添加start codes）
        let annexBData = convertToAnnexB(data)
        
        delegate?.videoEncoder(self, didEncodeFrame: annexBData, timestamp: timestamp, isKeyFrame: isKeyFrame)
    }
    
    private func isKeyFrameSampleBuffer(_ sampleBuffer: CMSampleBuffer) -> Bool {
        guard let attachments = CMSampleBufferGetSampleAttachmentsArray(sampleBuffer, createIfNecessary: false) as? [[CFString: Any]],
              let attachment = attachments.first else {
            return false
        }
        
        return attachment[kCMSampleAttachmentKey_NotSync] == nil
    }
    
    private func convertToAnnexB(_ data: Data) -> Data {
        var annexBData = Data()
        var offset = 0
        
        while offset < data.count {
            // 读取NALU长度（前4字节）
            guard offset + 4 <= data.count else { break }
            
            let naluLength = data.subdata(in: offset..<offset+4).withUnsafeBytes { bytes in
                bytes.load(as: UInt32.self).bigEndian
            }
            
            offset += 4
            
            guard offset + Int(naluLength) <= data.count else { break }
            
            // 添加start code (0x00000001)
            annexBData.append(contentsOf: [0x00, 0x00, 0x00, 0x01])
            
            // 添加NALU数据
            annexBData.append(data.subdata(in: offset..<offset+Int(naluLength)))
            
            offset += Int(naluLength)
        }
        
        return annexBData
    }
}

// MARK: - 错误定义

enum VideoEncoderError: Error, LocalizedError {
    case sessionCreationFailed(OSStatus)
    case configurationFailed(String, OSStatus)
    case sessionNotReady
    case encodingFailed(OSStatus)
    case invalidSampleBuffer
    case dataCopyFailed(OSStatus)
    
    var errorDescription: String? {
        switch self {
        case .sessionCreationFailed(let status):
            return "Failed to create compression session: \(status)"
        case .configurationFailed(let property, let status):
            return "Failed to configure \(property): \(status)"
        case .sessionNotReady:
            return "Compression session not ready"
        case .encodingFailed(let status):
            return "Encoding failed: \(status)"
        case .invalidSampleBuffer:
            return "Invalid sample buffer"
        case .dataCopyFailed(let status):
            return "Failed to copy data: \(status)"
        }
    }
}

// MARK: - 工具扩展

extension VideoEncoder {
    // 从CMSampleBuffer创建CVPixelBuffer（用于ReplayKit集成）
    static func pixelBuffer(from sampleBuffer: CMSampleBuffer) -> CVPixelBuffer? {
        return CMSampleBufferGetImageBuffer(sampleBuffer)
    }
    
    // 获取推荐的编码参数
    static func recommendedSettings(for size: CGSize) -> (width: Int32, height: Int32, bitrate: Int32) {
        let width = Int32(size.width)
        let height = Int32(size.height)
        
        // 根据分辨率计算推荐码率
        let pixels = width * height
        let bitrate: Int32
        
        switch pixels {
        case 0..<(640*480):
            bitrate = 1000000 // 1 Mbps
        case (640*480)..<(1280*720):
            bitrate = 2000000 // 2 Mbps
        case (1280*720)..<(1920*1080):
            bitrate = 4000000 // 4 Mbps
        default:
            bitrate = 6000000 // 6 Mbps
        }
        
        return (width, height, bitrate)
    }
}
