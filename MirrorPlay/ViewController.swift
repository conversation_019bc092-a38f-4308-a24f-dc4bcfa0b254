//
//  ViewController.swift
//  MirrorPlay
//
//  Created by cf110 on 2025/7/24.
//

import UIKit
import ReplayKit
import CoreBluetooth

class ViewController: UIViewController {

    // MARK: - UI Elements

    @IBOutlet weak var statusLabel: UILabel!
    @IBOutlet weak var startStopButton: UIButton!
    @IBOutlet weak var ipAddressLabel: UILabel!
    @IBOutlet weak var bluetoothStatusLabel: UILabel!
    @IBOutlet weak var connectedDevicesLabel: UILabel!
    @IBOutlet weak var qualitySegmentedControl: UISegmentedControl!

    // MARK: - Properties

    private var bluetoothManager: BluetoothManager?
    private var networkManager: NetworkManager?
    private var isStreaming = false
    private var connectedClients: Set<String> = []

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupManagers()
        updateUI()
    }

    override func viewWillAppear(_ animated: <PERSON><PERSON>) {
        super.viewWillAppear(animated)
        updateNetworkInfo()
    }

    // MARK: - Setup

    private func setupUI() {
        title = "MirrorPlay"

        // 配置状态标签
        statusLabel.text = "准备就绪"
        statusLabel.textAlignment = .center
        statusLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)

        // 配置开始/停止按钮
        startStopButton.setTitle("开始投屏", for: .normal)
        startStopButton.backgroundColor = .systemBlue
        startStopButton.layer.cornerRadius = 8
        startStopButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)

        // 配置质量选择
        qualitySegmentedControl.removeAllSegments()
        qualitySegmentedControl.insertSegment(withTitle: "低", at: 0, animated: false)
        qualitySegmentedControl.insertSegment(withTitle: "中", at: 1, animated: false)
        qualitySegmentedControl.insertSegment(withTitle: "高", at: 2, animated: false)
        qualitySegmentedControl.selectedSegmentIndex = 1

        // 配置信息标签
        ipAddressLabel.font = UIFont.systemFont(ofSize: 14)
        bluetoothStatusLabel.font = UIFont.systemFont(ofSize: 14)
        connectedDevicesLabel.font = UIFont.systemFont(ofSize: 14)
    }

    private func setupManagers() {
        // 设置蓝牙管理器
        bluetoothManager = BluetoothManager()
        bluetoothManager?.delegate = self

        // 设置网络管理器
        networkManager = NetworkManager()
        networkManager?.delegate = self
    }

    private func updateUI() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 更新按钮状态
            if self.isStreaming {
                self.startStopButton.setTitle("停止投屏", for: .normal)
                self.startStopButton.backgroundColor = .systemRed
                self.statusLabel.text = "正在投屏"
                self.statusLabel.textColor = .systemGreen
            } else {
                self.startStopButton.setTitle("开始投屏", for: .normal)
                self.startStopButton.backgroundColor = .systemBlue
                self.statusLabel.text = "准备就绪"
                self.statusLabel.textColor = .label
            }

            // 更新连接设备数量
            self.connectedDevicesLabel.text = "已连接设备: \(self.connectedClients.count)"

            // 更新网络信息
            self.updateNetworkInfo()
        }
    }

    private func updateNetworkInfo() {
        if let ipAddress = networkManager?.getLocalIPAddress() {
            ipAddressLabel.text = "IP地址: \(ipAddress):8888"
        } else {
            ipAddressLabel.text = "IP地址: 未获取到"
        }
    }

    // MARK: - Actions

    @IBAction func startStopButtonTapped(_ sender: UIButton) {
        if isStreaming {
            stopStreaming()
        } else {
            startStreaming()
        }
    }

    @IBAction func qualityChanged(_ sender: UISegmentedControl) {
        // 质量改变时的处理
        let qualityNames = ["低质量", "中等质量", "高质量"]
        let selectedQuality = qualityNames[sender.selectedSegmentIndex]

        showAlert(title: "质量设置", message: "已选择\(selectedQuality)，将在下次开始投屏时生效")
    }

    // MARK: - Streaming Control

    private func startStreaming() {
        // 检查权限
        guard checkPermissions() else { return }

        // 启动网络服务器
        do {
            try networkManager?.startServer()
        } catch {
            showAlert(title: "错误", message: "无法启动网络服务器: \(error.localizedDescription)")
            return
        }

        // 启动蓝牙广播
        bluetoothManager?.startAdvertising()

        // 启动屏幕录制
        startScreenRecording()
    }

    private func stopStreaming() {
        // 停止屏幕录制
        RPScreenRecorder.shared().stopRecording { [weak self] error in
            if let error = error {
                print("Stop recording error: \(error)")
            }

            DispatchQueue.main.async {
                self?.isStreaming = false
                self?.updateUI()
            }
        }

        // 停止网络服务器
        networkManager?.stopServer()

        // 停止蓝牙广播
        bluetoothManager?.stopAdvertising()

        // 清空连接列表
        connectedClients.removeAll()
    }

    private func startScreenRecording() {
        let recorder = RPScreenRecorder.shared()

        recorder.startRecording { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.showAlert(title: "录屏失败", message: error.localizedDescription)
                } else {
                    self?.isStreaming = true
                    self?.updateUI()
                }
            }
        }
    }

    // MARK: - Permissions

    private func checkPermissions() -> Bool {
        // 检查录屏权限
        if !RPScreenRecorder.shared().isAvailable {
            showAlert(title: "不支持", message: "此设备不支持屏幕录制")
            return false
        }

        return true
    }

    // MARK: - Utility

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - BluetoothManagerDelegate

extension ViewController: BluetoothManagerDelegate {
    func bluetoothManager(_ manager: BluetoothManager, didReceiveCommand command: ControlCommand) {
        DispatchQueue.main.async { [weak self] in
            switch command {
            case .startMirroring:
                if !self?.isStreaming ?? false {
                    self?.startStreaming()
                }
            case .stopMirroring:
                if self?.isStreaming ?? false {
                    self?.stopStreaming()
                }
            case .pauseMirroring:
                // 暂停功能可以在这里实现
                break
            case .resumeMirroring:
                // 恢复功能可以在这里实现
                break
            default:
                break
            }
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didUpdateStatus status: MirroringStatus) {
        DispatchQueue.main.async { [weak self] in
            switch status {
            case .idle:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 空闲"
            case .connecting:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 连接中"
            case .connected:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 已连接"
            case .mirroring:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 投屏中"
            case .paused:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 已暂停"
            case .error:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 错误"
            }
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didConnectToDevice device: CBPeripheral) {
        DispatchQueue.main.async { [weak self] in
            self?.bluetoothStatusLabel.text = "蓝牙状态: 已连接到 \(device.name ?? "未知设备")"
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didDisconnectFromDevice device: CBPeripheral) {
        DispatchQueue.main.async { [weak self] in
            self?.bluetoothStatusLabel.text = "蓝牙状态: 已断开连接"
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "蓝牙错误", message: error.localizedDescription)
        }
    }
}

// MARK: - NetworkManagerDelegate

extension ViewController: NetworkManagerDelegate {
    func networkManager(_ manager: NetworkManager, didReceiveVideoFrame data: Data, timestamp: UInt64) {
        // 主应用通常不需要接收视频帧
    }

    func networkManager(_ manager: NetworkManager, didReceiveAudioFrame data: Data, timestamp: UInt64) {
        // 主应用通常不需要接收音频帧
    }

    func networkManager(_ manager: NetworkManager, didReceiveControlCommand data: Data) {
        guard let command = String(data: data, encoding: .utf8) else { return }

        DispatchQueue.main.async { [weak self] in
            switch command {
            case "start":
                if !(self?.isStreaming ?? false) {
                    self?.startStreaming()
                }
            case "stop":
                if self?.isStreaming ?? false {
                    self?.stopStreaming()
                }
            case "status":
                // 发送当前状态
                let status = self?.isStreaming ?? false ? "streaming" : "idle"
                let statusData = status.data(using: .utf8) ?? Data()
                manager.sendControlCommand(statusData)
            default:
                print("Unknown network command: \(command)")
            }
        }
    }

    func networkManager(_ manager: NetworkManager, clientDidConnect client: String) {
        DispatchQueue.main.async { [weak self] in
            self?.connectedClients.insert(client)
            self?.updateUI()
            print("Network client connected: \(client)")
        }
    }

    func networkManager(_ manager: NetworkManager, clientDidDisconnect client: String) {
        DispatchQueue.main.async { [weak self] in
            self?.connectedClients.remove(client)
            self?.updateUI()
            print("Network client disconnected: \(client)")
        }
    }

    func networkManager(_ manager: NetworkManager, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "网络错误", message: error.localizedDescription)
        }
    }
}

