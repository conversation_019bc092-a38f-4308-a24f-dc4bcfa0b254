//
//  ViewController.swift
//  MirrorPlay - 主控制界面
//
//  这个文件是投屏应用的主控制界面，负责：
//  1. 显示应用状态和连接信息
//  2. 控制投屏的开始和停止
//  3. 管理蓝牙和网络连接
//  4. 提供质量设置选项
//

import UIKit
import ReplayKit
import CoreBluetooth

class ViewController: UIViewController {

    // MARK: - UI Elements (界面元素)
    // 这些是界面上的各种控件，我们用代码创建而不是Storyboard

    // 状态显示标签 - 显示当前投屏状态（准备就绪/正在投屏等）
    private var statusLabel: UILabel!

    // 开始/停止投屏按钮 - 主要的控制按钮
    private var startStopButton: UIButton!

    // IP地址显示标签 - 显示本机IP地址，供接收端连接使用
    private var ipAddressLabel: UILabel!

    // 蓝牙状态标签 - 显示蓝牙连接状态
    private var bluetoothStatusLabel: UILabel!

    // 已连接设备数量标签 - 显示有多少设备连接到这台设备
    private var connectedDevicesLabel: UILabel!

    // 质量选择控件 - 让用户选择投屏质量（低/中/高）
    private var qualitySegmentedControl: UISegmentedControl!

    // 切换到接收端按钮 - 跳转到接收端界面
    private var switchToPlayerButton: UIButton!
    
    private var testExtensionButton: UIButton!

    // MARK: - Properties (属性变量)
    // 这些是类的内部变量，用来管理应用的状态和功能模块

    // 蓝牙管理器 - 负责蓝牙设备的发现、连接和通信
    private var bluetoothManager: BluetoothManager?

    // 网络管理器 - 负责WiFi网络的数据传输
    private var networkManager: NetworkManager?

    // 投屏状态标志 - true表示正在投屏，false表示未投屏
    private var isStreaming = false

    // 已连接的客户端集合 - 存储所有连接到这台设备的客户端ID
    private var connectedClients: Set<String> = []

    // MARK: - Lifecycle (生命周期方法)
    // 这些是iOS系统自动调用的方法，在界面的不同阶段执行

    // 界面加载完成后调用 - 只调用一次
    override func viewDidLoad() {
        super.viewDidLoad()

        // 设置界面元素
        setupUI()

        // 初始化功能模块（蓝牙、网络等）
        setupManagers()

        // 更新界面显示
        updateUI()
    }

    // 界面即将显示时调用 - 每次显示都会调用
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        // 更新网络信息显示
        updateNetworkInfo()
    }

    // MARK: - Setup (初始化设置)

    // 设置用户界面 - 创建和配置所有的界面元素
    private func setupUI() {
        // 设置导航栏标题
        title = "MirrorPlay - 发送端"

        // 设置背景颜色为系统默认背景色（支持深色模式）
        view.backgroundColor = UIColor.systemBackground

        // 创建所有界面元素
        createUIElements()

        // 设置界面元素的布局约束
        setupConstraints()

        // 配置界面元素的样式和初始值
        configureUIElements()
    }

    // 创建所有界面元素
    private func createUIElements() {
        // 创建状态显示标签
        statusLabel = UILabel()
        statusLabel.text = "准备就绪"
        statusLabel.textAlignment = .center  // 文字居中对齐
        statusLabel.font = UIFont.systemFont(ofSize: 20, weight: .bold)  // 粗体字体
        statusLabel.textColor = .label  // 系统标签颜色（支持深色模式）

        // 创建开始/停止投屏按钮
        startStopButton = UIButton(type: .system)
        startStopButton.setTitle("开始投屏", for: .normal)
        startStopButton.backgroundColor = .systemBlue  // 系统蓝色
        startStopButton.setTitleColor(.white, for: .normal)  // 白色文字
        startStopButton.layer.cornerRadius = 12  // 圆角
        startStopButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        // 添加点击事件
        startStopButton.addTarget(self, action: #selector(startStopButtonTapped), for: .touchUpInside)

        // 创建质量选择控件
        qualitySegmentedControl = UISegmentedControl(items: ["低质量", "中等质量", "高质量"])
        qualitySegmentedControl.selectedSegmentIndex = 1  // 默认选择中等质量
        qualitySegmentedControl.backgroundColor = .systemGray6
        // 添加值改变事件
        qualitySegmentedControl.addTarget(self, action: #selector(qualityChanged), for: .valueChanged)

        // 创建IP地址显示标签
        ipAddressLabel = UILabel()
        ipAddressLabel.text = "IP地址: 获取中..."
        ipAddressLabel.font = UIFont.systemFont(ofSize: 16)
        ipAddressLabel.textColor = .secondaryLabel
        ipAddressLabel.textAlignment = .center

        // 创建蓝牙状态标签
        bluetoothStatusLabel = UILabel()
        bluetoothStatusLabel.text = "蓝牙状态: 初始化中"
        bluetoothStatusLabel.font = UIFont.systemFont(ofSize: 16)
        bluetoothStatusLabel.textColor = .secondaryLabel
        bluetoothStatusLabel.textAlignment = .center

        // 创建已连接设备数量标签
        connectedDevicesLabel = UILabel()
        connectedDevicesLabel.text = "已连接设备: 0"
        connectedDevicesLabel.font = UIFont.systemFont(ofSize: 16)
        connectedDevicesLabel.textColor = .secondaryLabel
        connectedDevicesLabel.textAlignment = .center

        // 创建切换到接收端按钮
        switchToPlayerButton = UIButton(type: .system)
        switchToPlayerButton.setTitle("切换到接收端", for: .normal)
        switchToPlayerButton.backgroundColor = .systemGreen
        switchToPlayerButton.setTitleColor(.white, for: .normal)
        switchToPlayerButton.layer.cornerRadius = 12
        switchToPlayerButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        switchToPlayerButton.addTarget(self, action: #selector(switchToPlayerTapped), for: .touchUpInside)

        // 创建测试扩展按钮
        testExtensionButton = UIButton(type: .system)
        testExtensionButton.setTitle("测试扩展", for: .normal)
        testExtensionButton.backgroundColor = .systemOrange
        testExtensionButton.setTitleColor(.white, for: .normal)
        testExtensionButton.layer.cornerRadius = 12
        testExtensionButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        testExtensionButton.addTarget(self, action: #selector(testExtensionTapped), for: .touchUpInside)

        // 将所有元素添加到主视图中
        view.addSubview(statusLabel)
        view.addSubview(startStopButton)
        view.addSubview(qualitySegmentedControl)
        view.addSubview(ipAddressLabel)
        view.addSubview(bluetoothStatusLabel)
        view.addSubview(connectedDevicesLabel)
        view.addSubview(switchToPlayerButton)
        view.addSubview(testExtensionButton)
    }

    // 设置界面元素的自动布局约束
    private func setupConstraints() {
        // 禁用自动调整大小掩码转换为约束（必须设置，否则会冲突）
        statusLabel.translatesAutoresizingMaskIntoConstraints = false
        startStopButton.translatesAutoresizingMaskIntoConstraints = false
        qualitySegmentedControl.translatesAutoresizingMaskIntoConstraints = false
        ipAddressLabel.translatesAutoresizingMaskIntoConstraints = false
        bluetoothStatusLabel.translatesAutoresizingMaskIntoConstraints = false
        connectedDevicesLabel.translatesAutoresizingMaskIntoConstraints = false
        switchToPlayerButton.translatesAutoresizingMaskIntoConstraints = false
        testExtensionButton.translatesAutoresizingMaskIntoConstraints = false

        // 激活所有约束 - 这些约束定义了每个元素的位置和大小
        NSLayoutConstraint.activate([
            // 状态标签约束 - 位于屏幕上方
            statusLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 40),
            statusLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            statusLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            statusLabel.heightAnchor.constraint(equalToConstant: 50),

            // 质量选择控件约束 - 位于状态标签下方
            qualitySegmentedControl.topAnchor.constraint(equalTo: statusLabel.bottomAnchor, constant: 30),
            qualitySegmentedControl.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            qualitySegmentedControl.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            qualitySegmentedControl.heightAnchor.constraint(equalToConstant: 44),

            // 开始/停止按钮约束 - 位于质量选择下方
            startStopButton.topAnchor.constraint(equalTo: qualitySegmentedControl.bottomAnchor, constant: 40),
            startStopButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            startStopButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            startStopButton.heightAnchor.constraint(equalToConstant: 60),

            // IP地址标签约束
            ipAddressLabel.topAnchor.constraint(equalTo: startStopButton.bottomAnchor, constant: 40),
            ipAddressLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            ipAddressLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            ipAddressLabel.heightAnchor.constraint(equalToConstant: 30),

            // 蓝牙状态标签约束
            bluetoothStatusLabel.topAnchor.constraint(equalTo: ipAddressLabel.bottomAnchor, constant: 15),
            bluetoothStatusLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            bluetoothStatusLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            bluetoothStatusLabel.heightAnchor.constraint(equalToConstant: 30),

            // 已连接设备标签约束
            connectedDevicesLabel.topAnchor.constraint(equalTo: bluetoothStatusLabel.bottomAnchor, constant: 15),
            connectedDevicesLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            connectedDevicesLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            connectedDevicesLabel.heightAnchor.constraint(equalToConstant: 30),

            // 切换到接收端按钮约束 - 位于屏幕底部
            switchToPlayerButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -40),
            switchToPlayerButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            switchToPlayerButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            switchToPlayerButton.heightAnchor.constraint(equalToConstant: 50),

            // 测试扩展按钮约束 - 位于切换到接收端按钮下方
            testExtensionButton.topAnchor.constraint(equalTo: switchToPlayerButton.bottomAnchor, constant: 20),
            testExtensionButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            testExtensionButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            testExtensionButton.heightAnchor.constraint(equalToConstant: 50)
        ])
    }

    // 配置界面元素的额外样式
    private func configureUIElements() {
        // 为按钮添加阴影效果
        startStopButton.layer.shadowColor = UIColor.black.cgColor
        startStopButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        startStopButton.layer.shadowOpacity = 0.1
        startStopButton.layer.shadowRadius = 4

        switchToPlayerButton.layer.shadowColor = UIColor.black.cgColor
        switchToPlayerButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        switchToPlayerButton.layer.shadowOpacity = 0.1
        switchToPlayerButton.layer.shadowRadius = 4

        testExtensionButton.layer.shadowColor = UIColor.black.cgColor
        testExtensionButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        testExtensionButton.layer.shadowOpacity = 0.1
        testExtensionButton.layer.shadowRadius = 4
    }

    // 初始化功能模块
    private func setupManagers() {
        print("🔧 初始化功能模块...")
        
        // 主动申请本地网络权限
        requestLocalNetworkPermission()
        
        // 创建并设置蓝牙管理器
        bluetoothManager = BluetoothManager()
        bluetoothManager?.delegate = self  // 设置代理，接收蓝牙事件回调

        // 创建并设置网络管理器
        networkManager = NetworkManager()
        networkManager?.delegate = self  // 设置代理，接收网络事件回调
        
        print("✅ 功能模块初始化完成")
    }
    
    // 申请本地网络权限
    private func requestLocalNetworkPermission() {
        print("🌐 申请本地网络权限...")
        
        // 尝试获取本地IP地址来触发权限申请
        if let ipAddress = networkManager?.getLocalIPAddress() {
            print("✅ 本地网络权限已获取，IP地址: \(ipAddress)")
        } else {
            print("⚠️ 无法获取本地IP地址，可能需要手动授权网络权限")
        }
    }

    // 更新界面显示 - 根据当前状态更新所有界面元素
    private func updateUI() {
        // 确保在主线程更新UI（iOS要求UI更新必须在主线程）
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 根据投屏状态更新按钮和状态标签
            if self.isStreaming {
                // 正在投屏状态
                self.startStopButton.setTitle("停止投屏", for: .normal)
                self.startStopButton.backgroundColor = .systemRed  // 红色表示停止
                self.statusLabel.text = "🔴 正在投屏"
                self.statusLabel.textColor = .systemRed

                // 投屏时禁用质量选择（避免中途改变参数）
                self.qualitySegmentedControl.isEnabled = false
            } else {
                // 未投屏状态
                self.startStopButton.setTitle("开始投屏", for: .normal)
                self.startStopButton.backgroundColor = .systemBlue  // 蓝色表示开始
                self.statusLabel.text = "⚪ 准备就绪"
                self.statusLabel.textColor = .systemBlue

                // 未投屏时允许选择质量
                self.qualitySegmentedControl.isEnabled = true
            }

            // 更新连接设备数量显示
            self.connectedDevicesLabel.text = "📱 已连接设备: \(self.connectedClients.count)"

            // 更新网络信息显示
            self.updateNetworkInfo()
        }
    }

    // 更新网络信息显示
    private func updateNetworkInfo() {
        // 从共享存储读取扩展的服务器信息
        if let ipAddress = SharedDataManager.shared.getServerIP(),
           let port = SharedDataManager.shared.getServerPort() {
            // 显示扩展的IP地址和端口号，供接收端连接使用
            ipAddressLabel.text = "🌐 IP地址: \(ipAddress):\(port)"
        } else {
            // 无法获取IP地址时的显示
            ipAddressLabel.text = "🌐 IP地址: 等待扩展启动..."
        }
    }

    // MARK: - Actions (用户交互事件处理)
    // 这些方法响应用户的点击、选择等操作

    // 开始/停止投屏按钮点击事件
    @objc private func startStopButtonTapped(_ sender: UIButton) {
        // 根据当前状态决定是开始还是停止投屏
        if isStreaming {
            stopStreaming()  // 如果正在投屏，则停止
        } else {
            startStreaming()  // 如果未投屏，则开始
        }
    }

    // 质量选择改变事件
    @objc private func qualityChanged(_ sender: UISegmentedControl) {
        // 获取选择的质量等级
        let qualityNames = ["低质量", "中等质量", "高质量"]
        let selectedQuality = qualityNames[sender.selectedSegmentIndex]

        // 显示提示信息
        showAlert(title: "质量设置", message: "已选择\(selectedQuality)，将在下次开始投屏时生效")

        // 这里可以保存用户的选择，在开始投屏时使用
        print("用户选择了质量等级: \(selectedQuality)")
    }

    // 切换到接收端按钮点击事件
    @objc private func switchToPlayerTapped(_ sender: UIButton) {
        // 创建接收端界面控制器
        let playerVC = PlayerViewController()

        // 使用导航控制器推送到接收端界面
        present(playerVC, animated: true)
    }

    // 测试扩展按钮点击事件
    @objc private func testExtensionTapped(_ sender: UIButton) {
        print("🔗 测试扩展按钮点击")
        
        // 详细诊断扩展
        diagnoseExtensions()
        
        // 测试ReplayKit扩展是否可用
        RPBroadcastActivityViewController.load { [weak self] activityViewController, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.showAlert(title: "测试失败", message: "无法加载广播活动视图控制器: \(error.localizedDescription)")
                    print("❌ 无法加载广播活动视图控制器: \(error)")
                } else if let activityViewController = activityViewController {
                    self?.showAlert(title: "测试成功", message: "广播活动视图控制器已加载，扩展可用")
                    print("✅ 广播活动视图控制器可用")
                    
                    // 显示扩展选择界面进行测试
                    activityViewController.delegate = self
                    self?.present(activityViewController, animated: true)
                } else {
                    self?.showAlert(title: "测试失败", message: "没有可用的广播扩展")
                    print("⚠️ 没有可用的广播扩展")
                }
            }
        }
    }
    
    // 详细诊断扩展
    private func diagnoseExtensions() {
        print("🔍 详细诊断扩展...")
        
        // 检查主应用Bundle
        if let bundleIdentifier = Bundle.main.bundleIdentifier {
            print("📦 主应用Bundle ID: \(bundleIdentifier)")
        }
        
        // 检查扩展目录
        let appBundle = Bundle.main.bundleURL
        let pluginsPath = appBundle.appendingPathComponent("PlugIns")
        let frameworksPath = appBundle.appendingPathComponent("Frameworks")
        
        print("📁 应用Bundle路径: \(appBundle)")
        print("🔌 扩展目录路径: \(pluginsPath)")
        print("📚 框架目录路径: \(frameworksPath)")
        
        // 检查扩展目录内容
        do {
            let pluginsContents = try FileManager.default.contentsOfDirectory(at: pluginsPath, includingPropertiesForKeys: nil)
            print("📋 扩展目录内容: \(pluginsContents)")
            
            for plugin in pluginsContents {
                if plugin.lastPathComponent.hasSuffix(".appex") {
                    print("✅ 找到扩展: \(plugin.lastPathComponent)")
                    
                    // 检查扩展的Info.plist
                    let infoPlistPath = plugin.appendingPathComponent("Info.plist")
                    if let infoPlist = NSDictionary(contentsOf: infoPlistPath) {
                        if let extensionInfo = infoPlist["NSExtension"] as? [String: Any] {
                            print("📄 扩展配置: \(extensionInfo)")
                        }
                    }
                }
            }
        } catch {
            print("❌ 无法读取扩展目录: \(error)")
        }
        
        // 检查框架目录内容
        do {
            let frameworksContents = try FileManager.default.contentsOfDirectory(at: frameworksPath, includingPropertiesForKeys: nil)
            print("📚 框架目录内容: \(frameworksContents)")
        } catch {
            print("❌ 无法读取框架目录: \(error)")
        }
    }

    // MARK: - Streaming Control (投屏控制逻辑)
    // 这些方法负责投屏功能的开始和停止

    // 开始投屏
    private func startStreaming() {
        print("🚀 开始投屏流程...")

        // 1. 检查设备权限和能力
        guard checkPermissions() else {
            print("❌ 权限检查失败")
            return
        }

        // 2. 启动网络服务器（用于接收客户端连接）
        do {
            try networkManager?.startServer()
            print("✅ 网络服务器启动成功")

            // 保存服务器信息到共享存储，供扩展使用
            if let port = networkManager?.currentServerPort,
               let ip = networkManager?.getLocalIPAddress() {
                SharedDataManager.shared.saveServerPort(port)
                SharedDataManager.shared.saveServerIP(ip)
                print("💾 服务器信息已保存到共享存储: \(ip):\(port)")
            }
        } catch {
            print("❌ 网络服务器启动失败: \(error)")
            showAlert(title: "网络错误", message: "无法启动网络服务器: \(error.localizedDescription)")
            return
        }

        // 3. 启动蓝牙广播（让其他设备可以发现这台设备）
        bluetoothManager?.startAdvertising()
        print("📡 蓝牙广播已启动")

        // 4. 启动屏幕录制（这是投屏的核心功能）
        startScreenRecording()
    }

    // 停止投屏
    private func stopStreaming() {
        print("🛑 停止投屏流程...")

        // 0. 更新共享状态
        SharedDataManager.shared.saveStreamingStatus(false)
        SharedDataManager.shared.saveConnectedClientsCount(0)

        // 1. 停止屏幕录制
        RPScreenRecorder.shared().stopRecording { [weak self] error,arg  in
            if let error = error {
                print("❌ 停止录屏时出错: \(error)")
            } else {
                print("✅ 屏幕录制已停止")
            }

            // 在主线程更新UI状态
            DispatchQueue.main.async {
                self?.isStreaming = false
                self?.updateUI()
            }
        }

        // 2. 停止网络服务器
        networkManager?.stopServer()
        print("🌐 网络服务器已停止")

        // 3. 停止蓝牙广播
        bluetoothManager?.stopAdvertising()
        print("📡 蓝牙广播已停止")

        // 4. 清空连接的客户端列表
        connectedClients.removeAll()
        print("🧹 已清空连接列表")
    }

    // 启动屏幕录制
    private func startScreenRecording() {
        print("📱 准备启动屏幕录制...")

        // 使用扩展选择界面启动录屏（这样才能调用扩展的 broadcastStarted）
        startScreenRecordingWithExtensionSelection()
    }

    // 使用扩展选择界面启动录屏（备用方法）
    private func startScreenRecordingWithExtensionSelection() {
        print("📱 使用扩展选择界面启动录屏...")

        // 首先检查扩展是否存在
        checkAvailableExtensions()

        RPBroadcastActivityViewController.load { [weak self] activityViewController, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("❌ 无法加载广播活动视图控制器: \(error)")
                    print("❌ 错误详情: \(error.localizedDescription)")
                    self?.showAlert(title: "录屏失败", message: "无法加载录屏扩展: \(error.localizedDescription)")
                    return
                }

                guard let activityViewController = activityViewController else {
                    print("❌ 没有可用的广播扩展")
                    self?.showAlert(title: "录屏失败", message: "没有找到可用的录屏扩展")
                    return
                }

                print("✅ 找到广播扩展，显示选择界面")
                print("📋 ActivityViewController: \(activityViewController)")

                // 设置代理
                activityViewController.delegate = self

                // 显示选择界面
                self?.present(activityViewController, animated: true)
            }
        }
    }

    // 检查可用的扩展
    private func checkAvailableExtensions() {
        print("🔍 检查可用的广播扩展...")

        // 检查主应用的Bundle
        let mainBundle = Bundle.main
        print("📦 主应用Bundle ID: \(mainBundle.bundleIdentifier ?? "未知")")
        print("📁 主应用路径: \(mainBundle.bundleURL)")

        // 检查扩展目录
        let plugInsURL = mainBundle.bundleURL.appendingPathComponent("PlugIns")
        print("🔌 扩展目录: \(plugInsURL)")

        do {
            let contents = try FileManager.default.contentsOfDirectory(at: plugInsURL, includingPropertiesForKeys: nil)
            print("📋 找到的扩展: \(contents)")

            for extensionURL in contents {
                if extensionURL.pathExtension == "appex" {
                    print("🔍 检查扩展: \(extensionURL.lastPathComponent)")

                    let infoPlistURL = extensionURL.appendingPathComponent("Info.plist")
                    if let plistData = try? Data(contentsOf: infoPlistURL),
                       let plist = try? PropertyListSerialization.propertyList(from: plistData, options: [], format: nil) as? [String: Any] {

                        if let nsExtension = plist["NSExtension"] as? [String: Any] {
                            print("📋 扩展信息:")
                            print("   - 扩展点: \(nsExtension["NSExtensionPointIdentifier"] ?? "未知")")
                            print("   - 主类: \(nsExtension["NSExtensionPrincipalClass"] ?? "未知")")
                        }
                    }
                }
            }
        } catch {
            print("❌ 无法读取扩展目录: \(error)")
        }
    }
    
    // 检查ReplayKit扩展
    private func checkReplayKitExtension() {
        print("🔍 检查ReplayKit扩展...")
        
        // 检查扩展是否可用
        if RPScreenRecorder.shared().isAvailable {
            print("✅ ReplayKit可用")
        } else {
            print("❌ ReplayKit不可用")
        }
        
        // 检查Bundle ID
        if let bundleIdentifier = Bundle.main.bundleIdentifier {
            print("📦 主应用Bundle ID: \(bundleIdentifier)")
        }
        
        // 检查扩展Bundle
        let extensionBundle = Bundle.main.bundleURL.appendingPathComponent("PlugIns")
        if extensionBundle != nil {
            print("🔌 扩展目录: \(extensionBundle)")
            do {
                let contents = try FileManager.default.contentsOfDirectory(at: extensionBundle, includingPropertiesForKeys: nil)
                print("📋 找到的扩展: \(contents)")
            } catch {
                print("❌ 无法读取扩展目录: \(error)")
            }
        }
        
        // 检查是否有可用的广播扩展
        RPBroadcastActivityViewController.load { activityViewController, error in
            if let error = error {
                print("❌ 无法加载广播活动视图控制器: \(error)")
            } else if let activityViewController = activityViewController {
                print("✅ 广播活动视图控制器可用")
                print("📋 扩展信息: \(activityViewController)")
            } else {
                print("⚠️ 没有可用的广播扩展")
            }
        }
    }

    // MARK: - Permissions (权限检查)

    // 检查设备是否支持所需功能
    private func checkPermissions() -> Bool {
        print("🔍 开始权限检查...")
        
        // 检查设备是否支持屏幕录制
        if !RPScreenRecorder.shared().isAvailable {
            print("❌ 设备不支持屏幕录制")
            showAlert(title: "设备不支持", message: "此设备不支持屏幕录制功能")
            return false
        }

        // 检查录屏器是否正在被其他应用使用
        if RPScreenRecorder.shared().isRecording {
            print("⚠️ 屏幕录制正在被其他应用使用")
            showAlert(title: "录屏冲突", message: "屏幕录制功能正在被其他应用使用，请先停止其他录屏应用")
            return false
        }
        
        // 检查网络权限
        checkNetworkPermission()
        
        // 检查蓝牙权限
        checkBluetoothPermission()

        print("✅ 权限检查通过")
        return true
    }
    
    // 检查网络权限
    private func checkNetworkPermission() {
        print("🌐 检查网络权限...")
        
        // 尝试获取本地IP地址
        if let ipAddress = networkManager?.getLocalIPAddress() {
            print("✅ 网络权限正常，IP地址: \(ipAddress)")
        } else {
            print("⚠️ 网络权限可能有问题")
            showAlert(title: "网络权限", message: "请在设置中允许MirrorPlay访问本地网络")
        }
    }
    
    // 检查蓝牙权限
    private func checkBluetoothPermission() {
        print("📡 检查蓝牙权限...")
        
        // 这里可以添加蓝牙权限检查逻辑
        // 由于蓝牙权限通常在运行时申请，这里只做基本检查
        print("✅ 蓝牙权限检查完成")
    }

    // MARK: - Utility (工具方法)

    // 显示提示对话框
    private func showAlert(title: String, message: String) {
        // 创建提示对话框
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)

        // 添加确定按钮
        alert.addAction(UIAlertAction(title: "确定", style: .default))

        // 显示对话框
        present(alert, animated: true)
    }

    // 检查扩展日志文件
    private func checkExtensionLogFiles() {
        print("📂 检查扩展日志文件...")

        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            print("❌ 无法获取文档目录")
            return
        }

        let logFiles = [
            "extension_class_loaded.txt",
            "extension_init.txt",
            "extension_broadcast_started.txt"
        ]

        for fileName in logFiles {
            let logFile = documentsPath.appendingPathComponent(fileName)

            if FileManager.default.fileExists(atPath: logFile.path) {
                do {
                    let content = try String(contentsOf: logFile)
                    print("✅ 找到日志文件 \(fileName):")
                    print("📄 内容: \(content)")
                } catch {
                    print("❌ 无法读取日志文件 \(fileName): \(error)")
                }
            } else {
                print("❌ 日志文件不存在: \(fileName)")
            }
        }

        // 列出文档目录中的所有文件
        do {
            let allFiles = try FileManager.default.contentsOfDirectory(at: documentsPath, includingPropertiesForKeys: nil)
            print("📁 文档目录中的所有文件: \(allFiles.map { $0.lastPathComponent })")
        } catch {
            print("❌ 无法列出文档目录: \(error)")
        }
    }
}

// MARK: - BluetoothManagerDelegate

extension ViewController: BluetoothManagerDelegate {
    func bluetoothManager(_ manager: BluetoothManager, didReceiveCommand command: ControlCommand) {
        DispatchQueue.main.async { [weak self] in
            switch command {
            case .startMirroring:
                if !(self?.isStreaming ?? false) {
                    self?.startStreaming()
                }
            case .stopMirroring:
                if self?.isStreaming ?? false {
                    self?.stopStreaming()
                }
            case .pauseMirroring:
                // 暂停功能可以在这里实现
                break
            case .resumeMirroring:
                // 恢复功能可以在这里实现
                break
            default:
                break
            }
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didUpdateStatus status: MirroringStatus) {
        DispatchQueue.main.async { [weak self] in
            switch status {
            case .idle:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 空闲"
            case .connecting:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 连接中"
            case .connected:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 已连接"
            case .mirroring:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 投屏中"
            case .paused:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 已暂停"
            case .error:
                self?.bluetoothStatusLabel.text = "蓝牙状态: 错误"
            }
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didConnectToDevice device: CBPeripheral) {
        DispatchQueue.main.async { [weak self] in
            self?.bluetoothStatusLabel.text = "蓝牙状态: 已连接到 \(device.name ?? "未知设备")"
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didDisconnectFromDevice device: CBPeripheral) {
        DispatchQueue.main.async { [weak self] in
            self?.bluetoothStatusLabel.text = "蓝牙状态: 已断开连接"
        }
    }

    func bluetoothManager(_ manager: BluetoothManager, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "蓝牙错误", message: error.localizedDescription)
        }
    }
}

// MARK: - NetworkManagerDelegate

extension ViewController: NetworkManagerDelegate {
    func networkManager(_ manager: NetworkManager, didReceiveVideoFrame data: Data, timestamp: UInt64) {
        // 主应用通常不需要接收视频帧
    }

    func networkManager(_ manager: NetworkManager, didReceiveAudioFrame data: Data, timestamp: UInt64) {
        // 主应用通常不需要接收音频帧
    }

    func networkManager(_ manager: NetworkManager, didReceiveControlCommand data: Data) {
        guard let command = String(data: data, encoding: .utf8) else { return }

        DispatchQueue.main.async { [weak self] in
            switch command {
            case "start":
                if !(self?.isStreaming ?? false) {
                    self?.startStreaming()
                }
            case "stop":
                if self?.isStreaming ?? false {
                    self?.stopStreaming()
                }
            case "status":
                // 发送当前状态
                let status = self?.isStreaming ?? false ? "streaming" : "idle"
                let statusData = status.data(using: .utf8) ?? Data()
                manager.sendControlCommand(statusData)
            default:
                print("Unknown network command: \(command)")
            }
        }
    }

    func networkManager(_ manager: NetworkManager, clientDidConnect client: String) {
        DispatchQueue.main.async { [weak self] in
            self?.connectedClients.insert(client)

            // 更新共享存储中的连接数量
            SharedDataManager.shared.saveConnectedClientsCount(self?.connectedClients.count ?? 0)

            self?.updateUI()
            print("Network client connected: \(client)")
        }
    }

    func networkManager(_ manager: NetworkManager, clientDidDisconnect client: String) {
        DispatchQueue.main.async { [weak self] in
            self?.connectedClients.remove(client)

            // 更新共享存储中的连接数量
            SharedDataManager.shared.saveConnectedClientsCount(self?.connectedClients.count ?? 0)

            self?.updateUI()
            print("Network client disconnected: \(client)")
        }
    }

    func networkManager(_ manager: NetworkManager, didEncounterError error: Error) {
        DispatchQueue.main.async { [weak self] in
            self?.showAlert(title: "网络错误", message: error.localizedDescription)
        }
    }
}

// MARK: - RPBroadcastActivityViewControllerDelegate

extension ViewController: RPBroadcastActivityViewControllerDelegate {
    func broadcastActivityViewController(_ broadcastActivityViewController: RPBroadcastActivityViewController, didFinishWith broadcastController: RPBroadcastController?, error: Error?) {
        
        // 关闭选择界面
        broadcastActivityViewController.dismiss(animated: true) {
            if let error = error {
                print("❌ 广播活动视图控制器错误: \(error)")
                self.showAlert(title: "录屏失败", message: error.localizedDescription)
                return
            }
            
            guard let broadcastController = broadcastController else {
                print("❌ 没有选择广播控制器")
                self.showAlert(title: "录屏失败", message: "没有选择录屏扩展")
                return
            }
            
            print("✅ 选择了广播控制器: \(broadcastController)")
            
            // 设置广播控制器代理
            broadcastController.delegate = self
            
            // 开始广播 - 这会触发扩展的 broadcastStarted 方法
            broadcastController.startBroadcast { error in
                DispatchQueue.main.async {
                    if let error = error {
                        print("❌ 开始广播失败: \(error)")
                        self.showAlert(title: "录屏失败", message: error.localizedDescription)
                    } else {
                        print("✅ 广播开始成功，扩展的 broadcastStarted 应该已被调用")
                        self.isStreaming = true
                        self.updateUI()

                        // 更新共享状态
                        SharedDataManager.shared.saveStreamingStatus(true)

                        // 等待一下，然后检查扩展是否真的启动了
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                            let extensionStatus = SharedDataManager.shared.getStreamingStatus()
                            print("🔍 检查扩展状态: \(extensionStatus)")

                            // 检查扩展日志文件
                            self.checkExtensionLogFiles()
                        }
                    }
                }
            }
        }
    }
}

// MARK: - RPBroadcastControllerDelegate

extension ViewController: RPBroadcastControllerDelegate {
    func broadcastController(_ broadcastController: RPBroadcastController, didFinishWithError error: Error?) {
        DispatchQueue.main.async {
            if let error = error {
                print("❌ 广播控制器错误: \(error)")
                self.showAlert(title: "录屏错误", message: error.localizedDescription)
            } else {
                print("✅ 广播控制器完成")
            }
            
            self.isStreaming = false
            self.updateUI()
        }
    }
    
    func broadcastController(_ broadcastController: RPBroadcastController, didUpdateServiceInfo serviceInfo: [String : NSCoding & NSObjectProtocol]) {
        print("📋 广播服务信息更新: \(serviceInfo)")
    }
    
    func broadcastController(
        _ broadcastController: RPBroadcastController,
        didUpdateBroadcast broadcastURL: URL
    ) {
        print("🌐 广播URL更新: \(broadcastURL)")

    }
}

