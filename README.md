# MirrorPlay - iOS 投屏应用

MirrorPlay 是一个基于苹果原生 API 的 iOS 投屏应用，使用 ReplayKit + WiFi + 蓝牙技术实现高质量的屏幕投射功能。

## 功能特性

- ✅ **原生技术栈**：完全基于苹果官方 API，无第三方依赖
- ✅ **高质量投屏**：支持 H.264 硬件编码，AAC 音频编码
- ✅ **WiFi 传输**：使用 TCP 协议进行高速数据传输
- ✅ **蓝牙控制**：通过蓝牙进行设备发现和控制信号传输
- ✅ **多质量选择**：支持低、中、高三种质量模式
- ✅ **实时音视频**：同时传输屏幕内容和音频
- ✅ **热点支持**：支持使用手机热点进行连接

## 技术架构

### 核心模块

1. **NetworkManager.swift** - 网络传输管理
   - TCP 服务器/客户端
   - 数据包协议定义
   - 连接管理

2. **BluetoothManager.swift** - 蓝牙控制管理
   - 设备发现和连接
   - 控制命令传输
   - 状态同步

3. **VideoEncoder.swift** - 视频编码器
   - H.264 硬件编码
   - 实时帧处理
   - 码率自适应

4. **AudioEncoder.swift** - 音频编码器
   - AAC 音频编码
   - 多声道支持
   - 低延迟处理

5. **VideoDecoder.swift** - 视频解码器
   - H.264 硬件解码
   - Annex-B 格式解析
   - 帧缓冲管理

6. **AudioDecoder.swift** - 音频解码器
   - AAC 音频解码
   - PCM 输出
   - 音频播放

### 应用结构

```
MirrorPlay/
├── MirrorPlay/                 # 主应用
│   ├── ViewController.swift    # 主控制界面
│   ├── PlayerViewController.swift # 接收端播放器
│   ├── NetworkManager.swift    # 网络管理
│   ├── BluetoothManager.swift  # 蓝牙管理
│   ├── VideoEncoder.swift      # 视频编码
│   ├── AudioEncoder.swift      # 音频编码
│   ├── VideoDecoder.swift      # 视频解码
│   └── AudioDecoder.swift      # 音频解码
├── upload/                     # ReplayKit 录屏扩展
│   └── SampleHandler.swift     # 录屏处理
└── uploadSetupUI/              # ReplayKit 设置界面
    └── BroadcastSetupViewController.swift
```

## 使用方法

### 发送端（投屏设备）

1. 打开 MirrorPlay 应用
2. 确保设备连接到 WiFi 网络或开启热点
3. 点击"开始投屏"按钮
4. 在系统录屏界面选择"MirrorPlay"
5. 配置投屏质量后开始录屏

### 接收端（显示设备）

1. 打开 MirrorPlay 应用
2. 切换到"接收端"界面
3. 输入发送端的 IP 地址
4. 点击"连接"按钮
5. 或使用蓝牙自动发现和连接

### 蓝牙控制

- 发送端：自动开启蓝牙广播
- 接收端：扫描并连接到发送端
- 支持远程控制开始/停止投屏

## 网络协议

### 数据包格式

```
| Type (1字节) | Timestamp (8字节) | DataSize (4字节) | Data (变长) |
```

### 包类型定义

- `0x01` - 视频帧
- `0x02` - 音频帧  
- `0x03` - 控制命令
- `0x04` - 心跳包

### 默认端口

- TCP 服务器：8888

## 蓝牙协议

### 服务 UUID
- 服务：`12345678-1234-1234-1234-123456789ABC`
- 控制特征：`12345678-1234-1234-1234-123456789ABD`
- 状态特征：`12345678-1234-1234-1234-123456789ABE`

### 控制命令

- `0x01` - 开始投屏
- `0x02` - 停止投屏
- `0x03` - 暂停投屏
- `0x04` - 恢复投屏
- `0x05` - 请求状态

## 系统要求

- iOS 13.0 或更高版本
- 支持 ReplayKit 的设备
- WiFi 或蓝牙连接

## 权限要求

- 蓝牙使用权限
- 本地网络访问权限
- 麦克风访问权限（录制音频）
- 屏幕录制权限

## 编译和运行

1. 使用 Xcode 打开 `MirrorPlay.xcodeproj`
2. 选择目标设备或模拟器
3. 点击运行按钮编译和安装

### 注意事项

- 录屏功能需要在真机上测试
- 确保设备在同一网络环境下
- 首次使用需要授权相关权限

## 性能优化

### 视频编码优化

- 使用硬件 H.264 编码器
- 自适应码率调整
- 关键帧间隔优化

### 音频编码优化

- AAC 低延迟编码
- 多声道支持
- 缓冲区管理

### 网络传输优化

- TCP 可靠传输
- 数据包分片处理
- 连接状态监控

### 蓝牙优化

- 低功耗连接
- 快速设备发现
- 连接状态管理

## 故障排除

### 常见问题

1. **无法开始录屏**
   - 检查 ReplayKit 权限
   - 确认设备支持录屏功能

2. **网络连接失败**
   - 检查 IP 地址是否正确
   - 确认防火墙设置
   - 验证网络连通性

3. **蓝牙连接问题**
   - 检查蓝牙权限
   - 确认蓝牙已开启
   - 重启蓝牙服务

4. **音视频不同步**
   - 检查网络延迟
   - 调整缓冲区大小
   - 优化编码参数

## 开发说明

### 扩展开发

- 支持自定义编码参数
- 可添加新的传输协议
- 支持多设备同时连接

### API 接口

所有核心功能都通过协议（Protocol）暴露，便于扩展和测试。

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系。
