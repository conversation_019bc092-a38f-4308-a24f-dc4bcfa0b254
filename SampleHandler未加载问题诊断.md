# SampleHandler 未加载问题诊断

## 🚨 问题现状

`SampleHandler` 的 `init()` 方法都没有执行，说明扩展根本没有被系统加载。

## 🔍 已确认的信息

✅ **扩展已正确嵌入**：
```bash
ls -la MirrorPlay.app/PlugIns/
# 结果：upload.appex 和 uploadSetupUI.appex 都存在
```

✅ **Info.plist 配置正确**：
```xml
"NSExtension" => {
  "NSExtensionPointIdentifier" => "com.apple.broadcast-services-upload"
  "NSExtensionPrincipalClass" => "upload.SampleHandler"
  "RPBroadcastProcessMode" => "RPBroadcastProcessModeSampleBuffer"
}
```

✅ **编译成功**：没有编译错误，扩展二进制文件存在

## 🎯 可能的原因

### 1. **系统扩展缓存问题**
iOS 系统会缓存扩展信息，可能需要清除缓存：

**解决方案**：
- 重启设备
- 删除应用后重新安装
- 等待系统刷新扩展缓存（可能需要几分钟）

### 2. **代码签名问题**
扩展和主应用的签名不匹配：

**检查方法**：
```bash
# 检查主应用签名
codesign -dv MirrorPlay.app

# 检查扩展签名
codesign -dv MirrorPlay.app/PlugIns/upload.appex
```

### 3. **Bundle ID 层次结构问题**
虽然看起来正确，但可能有细微问题：

**当前配置**：
- 主应用：`com.test.MirrorPlay.play`
- 扩展：`com.test.MirrorPlay.play.upload`

### 4. **Provisioning Profile 问题**
扩展可能没有包含在 Provisioning Profile 中。

### 5. **iOS 版本兼容性**
某些 iOS 版本对扩展加载有特殊要求。

## 🧪 诊断步骤

### 步骤1：重启设备测试
1. 完全关闭设备
2. 重新启动
3. 重新安装应用
4. 测试扩展加载

### 步骤2：使用系统日志
在 Xcode 中查看设备日志：
```
Window → Devices and Simulators → 选择设备 → Open Console
```

搜索关键词：
- `SampleHandler`
- `upload.appex`
- `broadcast`
- `extension`

### 步骤3：简化测试
创建一个最简单的 SampleHandler：

```swift
import ReplayKit

@objc(SampleHandler)
class SampleHandler: RPBroadcastSampleHandler {
    
    override init() {
        super.init()
        print("🔥 SampleHandler 初始化成功！")
        
        // 写入文件确保被调用
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let logFile = documentsPath.appendingPathComponent("extension_log.txt")
        try? "SampleHandler initialized at \(Date())".write(to: logFile, atomically: true, encoding: .utf8)
    }
    
    override func broadcastStarted(withSetupInfo setupInfo: [String : NSObject]?) {
        print("🎬 broadcastStarted 被调用！")
        
        // 写入文件确保被调用
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let logFile = documentsPath.appendingPathComponent("broadcast_log.txt")
        try? "broadcastStarted called at \(Date())".write(to: logFile, atomically: true, encoding: .utf8)
    }
}
```

### 步骤4：检查系统扩展注册
使用私有 API 检查（仅用于调试）：

```swift
// 在主应用中添加
private func checkSystemExtensions() {
    if let extensionItems = try? NSExtensionItem.extensionItems(matching: NSPredicate(format: "TRUEPREDICATE")) {
        print("系统中的扩展: \(extensionItems)")
    }
}
```

## 🔧 修复尝试

### 尝试1：修改 Info.plist
将 `NSExtensionPrincipalClass` 改为完全限定名：

```xml
<key>NSExtensionPrincipalClass</key>
<string>upload.SampleHandler</string>
```

### 尝试2：添加 @objc 标记
```swift
@objc(SampleHandler)
class SampleHandler: RPBroadcastSampleHandler {
    // ...
}
```

### 尝试3：检查模块导入
确保所有必要的框架都被导入：

```swift
import ReplayKit
import Foundation
import UIKit
```

### 尝试4：使用不同的启动方式
尝试直接使用 RPScreenRecorder：

```swift
let recorder = RPScreenRecorder.shared()
recorder.startRecording { error in
    // 这种方式不会使用扩展
}
```

对比使用 RPBroadcastController：

```swift
// 这种方式会使用扩展
broadcastController.startBroadcast { error in
    // 应该触发扩展
}
```

## 📱 真机测试要点

1. **确保在真机上测试**：扩展在模拟器上可能不工作
2. **检查设备日志**：使用 Xcode 的 Console 查看系统日志
3. **重启设备**：清除系统扩展缓存
4. **重新安装应用**：确保扩展被正确注册

## 🆘 最后的诊断方法

如果以上都不行，创建一个全新的最小项目：

1. 创建新的 iOS 项目
2. 添加 Broadcast Upload Extension
3. 只保留最基本的代码
4. 测试是否能加载

这样可以确定是项目配置问题还是系统问题。

## 📝 测试记录

```
测试时间：_______
设备型号：_______
iOS版本：_______

□ 重启设备后测试
□ 重新安装应用后测试
□ 检查系统日志
□ 简化 SampleHandler 代码
□ 创建最小测试项目

发现的问题：
1. _______
2. _______

解决方案：
1. _______
2. _______
```

## 🎯 下一步行动

1. **立即尝试**：重启设备，重新安装应用
2. **检查日志**：在 Xcode Console 中查找相关错误信息
3. **简化代码**：使用最简单的 SampleHandler 实现
4. **如果仍然不行**：创建全新的测试项目

这个问题很可能是系统级别的扩展注册问题，需要通过重启设备和重新安装来解决。
