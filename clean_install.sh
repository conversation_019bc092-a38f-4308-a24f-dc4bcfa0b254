#!/bin/bash

echo "🧹 开始清理和重新安装..."

# 1. 清理Xcode缓存
echo "📦 清理Xcode缓存..."
rm -rf ~/Library/Developer/Xcode/DerivedData/*
rm -rf ~/Library/Caches/com.apple.dt.Xcode/*

# 2. 停止所有iOS模拟器
echo "📱 停止iOS模拟器..."
xcrun simctl shutdown all 2>/dev/null || true

# 3. 删除应用（如果已安装）
echo "🗑️ 删除已安装的应用..."
xcrun simctl uninstall booted com.test.MirrorPlay.play 2>/dev/null || true

# 4. 清理构建产物
echo "🔨 清理构建产物..."
xcodebuild clean -project MirrorPlay.xcodeproj -scheme MirrorPlay 2>/dev/null || true

echo "✅ 清理完成！"
echo "🚀 现在请在Xcode中重新Build & Run" 