#!/bin/bash

echo "🧹 清理MirrorPlay应用和扩展..."

# 获取设备UDID（如果连接了设备）
DEVICE_UDID=""
if xcrun devicectl list devices | grep -q "iPhone\|iPad"; then
    DEVICE_UDID=$(xcrun devicectl list devices | grep "iPhone\|iPad" | head -1 | awk '{print $1}')
    echo "📱 找到设备: $DEVICE_UDID"
fi

# 删除应用（如果设备连接）
if [ ! -z "$DEVICE_UDID" ]; then
    echo "🗑️ 删除设备上的应用..."
    xcrun devicectl device install app --device $DEVICE_UDID --uninstall com.test.MirrorPlay.play
fi

echo "🔨 清理Xcode构建缓存..."
# 清理Xcode构建缓存
rm -rf ~/Library/Developer/Xcode/DerivedData/MirrorPlay-*
rm -rf ~/Library/Developer/Xcode/DerivedData/*/Build/Products/Debug-iphoneos/MirrorPlay.app
rm -rf ~/Library/Developer/Xcode/DerivedData/*/Build/Products/Debug-iphoneos/upload.appex

echo "✅ 清理完成"
echo ""
echo "📋 下一步操作："
echo "1. 在Xcode中执行 Product -> Clean Build Folder"
echo "2. 重新构建项目 (Cmd+B)"
echo "3. 重新安装到设备 (Cmd+R)"
echo "4. 在设备设置中检查权限"
echo "   - 设置 -> 屏幕录制 -> MirrorPlay"
echo "   - 设置 -> 隐私与安全性 -> 本地网络 -> MirrorPlay" 