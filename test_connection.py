#!/usr/bin/env python3
"""
MirrorPlay 网络连接测试脚本
用于测试发送端和接收端之间的网络连接
"""

import socket
import struct
import time
import threading

class MirrorPlayTester:
    def __init__(self, host='*************', port=8888):
        self.host = host
        self.port = port
        self.socket = None
        self.is_connected = False
        
    def connect(self):
        """连接到MirrorPlay服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5)
            self.socket.connect((self.host, self.port))
            self.is_connected = True
            print(f"✅ 成功连接到 {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.socket:
            self.socket.close()
            self.is_connected = False
            print("🔌 连接已断开")
    
    def send_test_packet(self, packet_type=0x01, data=b"test"):
        """发送测试数据包"""
        if not self.is_connected:
            print("❌ 未连接到服务器")
            return False
        
        try:
            # 创建数据包: 类型(1) + 时间戳(8) + 数据大小(4) + 数据
            timestamp = int(time.time() * 1000)
            data_size = len(data)
            
            packet = struct.pack('<BQ4s', packet_type, timestamp, struct.pack('<I', data_size))
            packet += data
            
            self.socket.send(packet)
            print(f"📤 发送测试包: 类型={packet_type}, 大小={len(packet)}字节")
            return True
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def receive_packets(self):
        """接收数据包"""
        if not self.is_connected:
            return
        
        print("📥 开始接收数据包...")
        try:
            while self.is_connected:
                # 接收包头 (13字节)
                header = self.socket.recv(13)
                if not header or len(header) < 13:
                    print("❌ 接收包头失败")
                    break
                
                # 解析包头
                packet_type, timestamp, data_size = struct.unpack('<BQ4s', header)
                data_size = struct.unpack('<I', data_size)[0]
                
                print(f"📦 接收到包头: 类型={packet_type}, 时间戳={timestamp}, 数据大小={data_size}")
                
                # 接收数据
                data = b''
                remaining = data_size
                while remaining > 0:
                    chunk = self.socket.recv(remaining)
                    if not chunk:
                        print("❌ 接收数据失败")
                        break
                    data += chunk
                    remaining -= len(chunk)
                
                if len(data) == data_size:
                    print(f"✅ 接收到完整数据包: {len(data)}字节")
                else:
                    print(f"❌ 数据包不完整: 期望{data_size}字节, 实际{len(data)}字节")
                    
        except Exception as e:
            print(f"❌ 接收数据时出错: {e}")
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 开始MirrorPlay网络连接测试")
        print(f"目标服务器: {self.host}:{self.port}")
        
        # 连接
        if not self.connect():
            return
        
        # 启动接收线程
        receive_thread = threading.Thread(target=self.receive_packets)
        receive_thread.daemon = True
        receive_thread.start()
        
        try:
            # 发送测试包
            for i in range(5):
                self.send_test_packet(0x01, f"test_data_{i}".encode())
                time.sleep(1)
            
            # 等待一段时间
            time.sleep(5)
            
        except KeyboardInterrupt:
            print("\n⏹️ 测试被用户中断")
        finally:
            self.disconnect()

if __name__ == "__main__":
    import sys
    
    # 获取命令行参数
    host = sys.argv[1] if len(sys.argv) > 1 else '*************'
    
    tester = MirrorPlayTester(host)
    tester.run_test() 