<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSLocalNetworkUsageDescription</key>
	<string>MirrorPlay扩展需要访问本地网络以进行设备间通信</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>MirrorPlay扩展需要蓝牙权限以进行设备发现和控制</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>MirrorPlay扩展需要蓝牙权限以进行设备发现和控制</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>MirrorPlay扩展需要麦克风权限以录制音频</string>
	<key>NSExtension</key>
	<dict>
		<key>NSExtensionPointIdentifier</key>
		<string>com.apple.broadcast-services-upload</string>
		<key>NSExtensionPrincipalClass</key>
		<string>upload.SampleHandler</string>
		<key>RPBroadcastProcessMode</key>
		<string>RPBroadcastProcessModeSampleBuffer</string>
	</dict>
	<key>NSExtensionAttributes</key>
	<dict>
		<key>NSExtensionActivationRule</key>
		<string>TRUEPREDICATE</string>
	</dict>

	<!-- 扩展基本信息 -->
	<key>CFBundleDisplayName</key>
	<string>MirrorPlay录屏</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
</dict>
</plist>
