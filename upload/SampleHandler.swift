//
//  SampleHandler.swift
//  upload
//
//  Created by cf110 on 2025/7/24.
//

import ReplayKit
import CoreMedia
import VideoToolbox
import Network

class SampleHandler: RPBroadcastSampleHandler {

    // 编码器
    private var videoEncoder: VideoEncoder?
    private var audioEncoder: AudioEncoder?

    // 网络管理器
    private var networkManager: NetworkManager?

    // 状态管理
    private var isStreaming = false
    private var setupInfo: [String: NSObject]?

    // 视频参数
    private var videoSize: CGSize = .zero
    private var lastVideoTimestamp: UInt64 = 0
    private var lastAudioTimestamp: UInt64 = 0

    override func broadcastStarted(withSetupInfo setupInfo: [String : NSObject]?) {
        print("Broadcast started with setup info: \(setupInfo ?? [:])")
        self.setupInfo = setupInfo

        // 初始化网络管理器
        setupNetworkManager()

        // 标记开始推流
        isStreaming = true
    }

    override func broadcastPaused() {
        print("Broadcast paused")
        isStreaming = false

        // 暂停编码器（保持连接）
        // 编码器会自动处理暂停状态
    }

    override func broadcastResumed() {
        print("Broadcast resumed")
        isStreaming = true
    }

    override func broadcastFinished() {
        print("Broadcast finished")
        isStreaming = false

        // 停止编码器
        videoEncoder?.stopEncoding()
        audioEncoder?.stopEncoding()

        // 停止网络传输
        networkManager?.stopServer()

        // 清理资源
        videoEncoder = nil
        audioEncoder = nil
        networkManager = nil
    }

    override func processSampleBuffer(_ sampleBuffer: CMSampleBuffer, with sampleBufferType: RPSampleBufferType) {
        guard isStreaming else { return }

        switch sampleBufferType {
        case RPSampleBufferType.video:
            handleVideoSampleBuffer(sampleBuffer)

        case RPSampleBufferType.audioApp:
            handleAudioSampleBuffer(sampleBuffer, isAppAudio: true)

        case RPSampleBufferType.audioMic:
            handleAudioSampleBuffer(sampleBuffer, isAppAudio: false)

        @unknown default:
            print("Unknown sample buffer type")
        }
    }

    // MARK: - 网络设置

    private func setupNetworkManager() {
        networkManager = NetworkManager()
        networkManager?.delegate = self

        do {
            try networkManager?.startServer()
            print("Network server started successfully")
        } catch {
            print("Failed to start network server: \(error)")
            finishBroadcastWithError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
        }
    }

    // MARK: - 视频处理

    private func handleVideoSampleBuffer(_ sampleBuffer: CMSampleBuffer) {
        guard let pixelBuffer = VideoEncoder.pixelBuffer(from: sampleBuffer) else {
            print("Failed to get pixel buffer from video sample")
            return
        }

        // 获取视频尺寸
        let width = CVPixelBufferGetWidth(pixelBuffer)
        let height = CVPixelBufferGetHeight(pixelBuffer)
        let currentSize = CGSize(width: width, height: height)

        // 如果尺寸改变，重新初始化编码器
        if currentSize != videoSize {
            videoSize = currentSize
            setupVideoEncoder(size: currentSize)
        }

        // 编码视频帧
        let presentationTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
        videoEncoder?.encodeFrame(pixelBuffer, timestamp: presentationTime)
    }

    private func setupVideoEncoder(size: CGSize) {
        // 停止旧的编码器
        videoEncoder?.stopEncoding()

        // 获取推荐设置
        let settings = VideoEncoder.recommendedSettings(for: size)

        // 创建新的编码器
        videoEncoder = VideoEncoder(
            width: settings.width,
            height: settings.height,
            bitrate: settings.bitrate,
            fps: 30
        )

        videoEncoder?.delegate = self

        do {
            try videoEncoder?.startEncoding()
            print("Video encoder started: \(settings.width)x\(settings.height)")
        } catch {
            print("Failed to start video encoder: \(error)")
            finishBroadcastWithError(NSError(domain: "VideoEncoderError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
        }
    }

    // MARK: - 音频处理

    private func handleAudioSampleBuffer(_ sampleBuffer: CMSampleBuffer, isAppAudio: Bool) {
        // 初始化音频编码器（如果需要）
        if audioEncoder == nil {
            setupAudioEncoder(sampleBuffer: sampleBuffer)
        }

        // 编码音频帧
        audioEncoder?.encodeAudioBuffer(sampleBuffer)
    }

    private func setupAudioEncoder(sampleBuffer: CMSampleBuffer) {
        guard let format = AudioEncoder.audioFormat(from: sampleBuffer) else {
            print("Failed to get audio format from sample buffer")
            return
        }

        // 获取推荐设置
        let settings = AudioEncoder.recommendedSettings(for: format)

        // 创建音频编码器
        audioEncoder = AudioEncoder(
            sampleRate: settings.sampleRate,
            channels: settings.channels,
            bitrate: settings.bitrate
        )

        audioEncoder?.delegate = self

        do {
            try audioEncoder?.startEncoding()
            print("Audio encoder started: \(settings.sampleRate) Hz, \(settings.channels) channels")
        } catch {
            print("Failed to start audio encoder: \(error)")
            finishBroadcastWithError(NSError(domain: "AudioEncoderError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
        }
    }
}

// MARK: - VideoEncoderDelegate

extension SampleHandler: VideoEncoderDelegate {
    func videoEncoder(_ encoder: VideoEncoder, didEncodeFrame data: Data, timestamp: UInt64, isKeyFrame: Bool) {
        // 发送视频帧到网络
        networkManager?.sendVideoFrame(data, timestamp: timestamp)
        lastVideoTimestamp = timestamp

        if isKeyFrame {
            print("Sent key frame: \(data.count) bytes at \(timestamp)")
        }
    }

    func videoEncoder(_ encoder: VideoEncoder, didEncounterError error: Error) {
        print("Video encoder error: \(error)")
        finishBroadcastWithError(NSError(domain: "VideoEncoderError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
    }
}

// MARK: - AudioEncoderDelegate

extension SampleHandler: AudioEncoderDelegate {
    func audioEncoder(_ encoder: AudioEncoder, didEncodeFrame data: Data, timestamp: UInt64) {
        // 发送音频帧到网络
        networkManager?.sendAudioFrame(data, timestamp: timestamp)
        lastAudioTimestamp = timestamp
    }

    func audioEncoder(_ encoder: AudioEncoder, didEncounterError error: Error) {
        print("Audio encoder error: \(error)")
        finishBroadcastWithError(NSError(domain: "AudioEncoderError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
    }
}

// MARK: - NetworkManagerDelegate

extension SampleHandler: NetworkManagerDelegate {
    func networkManager(_ manager: NetworkManager, didReceiveVideoFrame data: Data, timestamp: UInt64) {
        // 在录屏扩展中通常不需要接收视频帧
    }

    func networkManager(_ manager: NetworkManager, didReceiveAudioFrame data: Data, timestamp: UInt64) {
        // 在录屏扩展中通常不需要接收音频帧
    }

    func networkManager(_ manager: NetworkManager, didReceiveControlCommand data: Data) {
        // 处理控制命令
        guard let command = String(data: data, encoding: .utf8) else { return }

        switch command {
        case "pause":
            broadcastPaused()
        case "resume":
            broadcastResumed()
        case "stop":
            broadcastFinished()
        default:
            print("Unknown control command: \(command)")
        }
    }

    func networkManager(_ manager: NetworkManager, clientDidConnect client: String) {
        print("Client connected: \(client)")

        // 发送当前状态信息
        let statusData = "streaming".data(using: .utf8) ?? Data()
        manager.sendControlCommand(statusData)
    }

    func networkManager(_ manager: NetworkManager, clientDidDisconnect client: String) {
        print("Client disconnected: \(client)")
    }

    func networkManager(_ manager: NetworkManager, didEncounterError error: Error) {
        print("Network error: \(error)")
        finishBroadcastWithError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
    }
}
