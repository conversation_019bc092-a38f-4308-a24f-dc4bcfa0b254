//
//  SampleHandler.swift
//  upload
//
//  Created by cf110 on 2025/7/24.
//

import ReplayKit
import CoreMedia
import VideoToolbox
import Network

class SampleHandler: RPBroadcastSampleHandler {

    // 编码器
    private var videoEncoder: VideoEncoder?
    private var audioEncoder: AudioEncoder?

    // 网络管理器
    private var networkManager: NetworkManager?

    // 状态管理
    private var isStreaming = false
    private var setupInfo: [String: NSObject]?

    // 视频参数
    private var videoSize: CGSize = .zero
    private var lastVideoTimestamp: UInt64 = 0
    private var lastAudioTimestamp: UInt64 = 0

    // MARK: - 初始化和生命周期

    override init() {
        super.init()
        print("🔧 ===== SampleHandler 初始化 =====")
        print("📦 扩展Bundle ID: \(Bundle.main.bundleIdentifier ?? "未知")")
        print("📁 扩展路径: \(Bundle.main.bundleURL)")
        print("🕐 初始化时间: \(Date())")
    }

    deinit {
        print("💀 ===== SampleHandler 销毁 =====")
    }

    override func broadcastStarted(withSetupInfo setupInfo: [String : NSObject]?) {
        print("🎬 ===== ReplayKit扩展启动 =====")
        print("� 启动时间: \(Date())")
        print("�📋 设置信息: \(setupInfo ?? [:])")
        print("🔧 扩展Bundle ID: \(Bundle.main.bundleIdentifier ?? "未知")")
        print("📁 扩展Bundle路径: \(Bundle.main.bundleURL)")
        print("🧵 当前线程: \(Thread.current)")

        self.setupInfo = setupInfo

        // 立即更新共享状态，表明扩展已启动
        SharedDataManager.shared.saveStreamingStatus(true)
        print("💾 已保存扩展启动状态到共享存储")

        // 初始化网络管理器
        setupNetworkManager()

        // 标记开始推流
        isStreaming = true
        print("✅ 推流状态已设置为true")

        // 发送测试数据包
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            print("🧪 发送测试数据包...")
            let testData = "Extension Started: \(Date())".data(using: .utf8) ?? Data()
            self.networkManager?.sendControlCommand(testData)
        }

        print("🎬 ===== broadcastStarted 方法执行完成 =====")
    }

    override func broadcastPaused() {
        print("⏸️ 推流已暂停")
        isStreaming = false

        // 暂停编码器（保持连接）
        // 编码器会自动处理暂停状态
    }

    override func broadcastResumed() {
        print("▶️ 推流已恢复")
        isStreaming = true
    }

    override func broadcastFinished() {
        print("⏹️ 推流已结束")
        isStreaming = false

        // 更新共享状态
        SharedDataManager.shared.saveStreamingStatus(false)

        // 停止编码器
        videoEncoder?.stopEncoding()
        audioEncoder?.stopEncoding()

        // 断开与主应用服务器的连接（不是停止服务器）
        // networkManager?.disconnect() // 如果有disconnect方法的话

        // 清理资源
        videoEncoder = nil
        audioEncoder = nil
        networkManager = nil

        print("🧹 扩展资源清理完成")
    }

    override func processSampleBuffer(_ sampleBuffer: CMSampleBuffer, with sampleBufferType: RPSampleBufferType) {
        guard isStreaming else { 
            print("⚠️ 推流未激活，跳过样本处理")
            return 
        }

        switch sampleBufferType {
        case RPSampleBufferType.video:
            print("📹 处理视频样本缓冲区")
            handleVideoSampleBuffer(sampleBuffer)

        case RPSampleBufferType.audioApp:
            print("🎵 处理应用音频样本缓冲区")
            handleAudioSampleBuffer(sampleBuffer, isAppAudio: true)

        case RPSampleBufferType.audioMic:
            print("🎤 处理麦克风音频样本缓冲区")
            handleAudioSampleBuffer(sampleBuffer, isAppAudio: false)

        @unknown default:
            print("❓ 未知样本缓冲区类型")
        }
    }

    // MARK: - 网络设置

    private func setupNetworkManager() {
        print("🌐 设置扩展网络管理器...")
        networkManager = NetworkManager()
        networkManager?.delegate = self

        // 从共享存储读取主应用的服务器信息
        guard let serverIP = SharedDataManager.shared.getServerIP(),
              let serverPort = SharedDataManager.shared.getServerPort() else {
            print("❌ 无法获取主应用服务器信息")
            finishBroadcastWithError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法获取服务器信息"]))
            return
        }

        print("📡 准备连接到主应用服务器: \(serverIP):\(serverPort)")

        // 连接到主应用的服务器
        networkManager?.connectToServer(host: serverIP, port: serverPort) { [weak self] result in
            switch result {
            case .success:
                print("✅ 扩展成功连接到主应用服务器")
                // 连接成功后，保存推流状态
                SharedDataManager.shared.saveStreamingStatus(true)
            case .failure(let error):
                print("❌ 扩展连接主应用服务器失败: \(error)")
                self?.finishBroadcastWithError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
            }
        }
    }

    // MARK: - 视频处理

    private func handleVideoSampleBuffer(_ sampleBuffer: CMSampleBuffer) {
        guard let pixelBuffer = VideoEncoder.pixelBuffer(from: sampleBuffer) else {
            print("❌ 无法从视频样本获取像素缓冲区")
            return
        }

        // 获取视频尺寸
        let width = CVPixelBufferGetWidth(pixelBuffer)
        let height = CVPixelBufferGetHeight(pixelBuffer)
        let currentSize = CGSize(width: width, height: height)

        // 如果尺寸改变，重新初始化编码器
        if currentSize != videoSize {
            videoSize = currentSize
            print("📐 视频尺寸改变: \(width)x\(height)")
            setupVideoEncoder(size: currentSize)
        }

        // 编码视频帧
        let presentationTime = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)
        videoEncoder?.encodeFrame(pixelBuffer, timestamp: presentationTime)
    }

    private func setupVideoEncoder(size: CGSize) {
        // 停止旧的编码器
        videoEncoder?.stopEncoding()

        // 获取推荐设置
        let settings = VideoEncoder.recommendedSettings(for: size)

        // 创建新的编码器
        videoEncoder = VideoEncoder(
            width: settings.width,
            height: settings.height,
            bitrate: settings.bitrate,
            fps: 30
        )

        videoEncoder?.delegate = self

        do {
            try videoEncoder?.startEncoding()
            print("Video encoder started: \(settings.width)x\(settings.height)")
        } catch {
            print("Failed to start video encoder: \(error)")
            finishBroadcastWithError(NSError(domain: "VideoEncoderError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
        }
    }

    // MARK: - 音频处理

    private func handleAudioSampleBuffer(_ sampleBuffer: CMSampleBuffer, isAppAudio: Bool) {
        // 初始化音频编码器（如果需要）
        if audioEncoder == nil {
            setupAudioEncoder(sampleBuffer: sampleBuffer)
        }

        // 编码音频帧
        audioEncoder?.encodeAudioBuffer(sampleBuffer)
    }

    private func setupAudioEncoder(sampleBuffer: CMSampleBuffer) {
        guard let format = AudioEncoder.audioFormat(from: sampleBuffer) else {
            print("Failed to get audio format from sample buffer")
            return
        }

        // 获取推荐设置
        let settings = AudioEncoder.recommendedSettings(for: format)

        // 创建音频编码器
        audioEncoder = AudioEncoder(
            sampleRate: settings.sampleRate,
            channels: settings.channels,
            bitrate: settings.bitrate
        )

        audioEncoder?.delegate = self

        do {
            try audioEncoder?.startEncoding()
            print("Audio encoder started: \(settings.sampleRate) Hz, \(settings.channels) channels")
        } catch {
            print("Failed to start audio encoder: \(error)")
            finishBroadcastWithError(NSError(domain: "AudioEncoderError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
        }
    }
}

// MARK: - VideoEncoderDelegate

extension SampleHandler: VideoEncoderDelegate {
    func videoEncoder(_ encoder: VideoEncoder, didEncodeFrame data: Data, timestamp: UInt64, isKeyFrame: Bool) {
        // 发送视频帧到网络
        print("📹 编码完成，发送视频帧: \(data.count) 字节, 关键帧: \(isKeyFrame)")
        networkManager?.sendVideoFrame(data, timestamp: timestamp)
        lastVideoTimestamp = timestamp

        if isKeyFrame {
            print("🔑 发送关键帧: \(data.count) 字节 at \(timestamp)")
        }
    }

    func videoEncoder(_ encoder: VideoEncoder, didEncounterError error: Error) {
        print("❌ 视频编码器错误: \(error)")
        finishBroadcastWithError(NSError(domain: "VideoEncoderError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
    }
}

// MARK: - AudioEncoderDelegate

extension SampleHandler: AudioEncoderDelegate {
    func audioEncoder(_ encoder: AudioEncoder, didEncodeFrame data: Data, timestamp: UInt64) {
        // 发送音频帧到网络
        networkManager?.sendAudioFrame(data, timestamp: timestamp)
        lastAudioTimestamp = timestamp
    }

    func audioEncoder(_ encoder: AudioEncoder, didEncounterError error: Error) {
        print("Audio encoder error: \(error)")
        finishBroadcastWithError(NSError(domain: "AudioEncoderError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
    }
}

// MARK: - NetworkManagerDelegate

extension SampleHandler: NetworkManagerDelegate {
    func networkManager(_ manager: NetworkManager, didReceiveVideoFrame data: Data, timestamp: UInt64) {
        // 在录屏扩展中通常不需要接收视频帧
    }

    func networkManager(_ manager: NetworkManager, didReceiveAudioFrame data: Data, timestamp: UInt64) {
        // 在录屏扩展中通常不需要接收音频帧
    }

    func networkManager(_ manager: NetworkManager, didReceiveControlCommand data: Data) {
        // 处理控制命令
        guard let command = String(data: data, encoding: .utf8) else { return }

        switch command {
        case "pause":
            broadcastPaused()
        case "resume":
            broadcastResumed()
        case "stop":
            broadcastFinished()
        default:
            print("Unknown control command: \(command)")
        }
    }

    func networkManager(_ manager: NetworkManager, clientDidConnect client: String) {
        print("✅ 客户端连接: \(client)")
        print("📡 开始发送视频流...")

        // 发送当前状态信息
        let statusData = "streaming".data(using: .utf8) ?? Data()
        manager.sendControlCommand(statusData)
    }

    func networkManager(_ manager: NetworkManager, clientDidDisconnect client: String) {
        print("❌ 客户端断开: \(client)")
    }

    func networkManager(_ manager: NetworkManager, didEncounterError error: Error) {
        print("❌ 网络错误: \(error)")
        finishBroadcastWithError(NSError(domain: "NetworkError", code: -1, userInfo: [NSLocalizedDescriptionKey: error.localizedDescription]))
    }
}
