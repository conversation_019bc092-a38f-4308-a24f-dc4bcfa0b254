//
//  BroadcastSetupViewController.swift
//  uploadSetupUI
//
//  Created by cf110 on 2025/7/24.
//

import ReplayKit
import UIKit

class BroadcastSetupViewController: UIViewController {

    // MARK: - UI Elements

    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var descriptionLabel: UILabel!
    @IBOutlet weak var qualitySegmentedControl: UISegmentedControl!
    @IBOutlet weak var startButton: UIButton!
    @IBOutlet weak var cancelButton: UIButton!

    // MARK: - Properties

    private var selectedQuality: StreamQuality = .medium

    enum StreamQuality: Int, CaseIterable {
        case low = 0
        case medium = 1
        case high = 2

        var title: String {
            switch self {
            case .low: return "低质量"
            case .medium: return "中等质量"
            case .high: return "高质量"
            }
        }

        var bitrate: Int32 {
            switch self {
            case .low: return 1000000    // 1 Mbps
            case .medium: return 2000000 // 2 Mbps
            case .high: return 4000000   // 4 Mbps
            }
        }
    }

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    // MARK: - Setup

    private func setupUI() {
        view.backgroundColor = UIColor.systemBackground

        // 配置标题
        titleLabel.text = "MirrorPlay 投屏设置"
        titleLabel.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        titleLabel.textAlignment = .center

        // 配置描述
        descriptionLabel.text = "选择投屏质量，然后点击开始投屏"
        descriptionLabel.font = UIFont.systemFont(ofSize: 16)
        descriptionLabel.textAlignment = .center
        descriptionLabel.textColor = .secondaryLabel
        descriptionLabel.numberOfLines = 0

        // 配置质量选择
        qualitySegmentedControl.removeAllSegments()
        for (index, quality) in StreamQuality.allCases.enumerated() {
            qualitySegmentedControl.insertSegment(withTitle: quality.title, at: index, animated: false)
        }
        qualitySegmentedControl.selectedSegmentIndex = StreamQuality.medium.rawValue
        qualitySegmentedControl.addTarget(self, action: #selector(qualityChanged), for: .valueChanged)

        // 配置开始按钮
        startButton.setTitle("开始投屏", for: .normal)
        startButton.backgroundColor = .systemBlue
        startButton.layer.cornerRadius = 8
        startButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        startButton.addTarget(self, action: #selector(startButtonTapped), for: .touchUpInside)

        // 配置取消按钮
        cancelButton.setTitle("取消", for: .normal)
        cancelButton.backgroundColor = .systemGray
        cancelButton.layer.cornerRadius = 8
        cancelButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        cancelButton.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)

        // 设置约束
        setupConstraints()
    }

    private func setupConstraints() {
        // 如果没有使用Storyboard，需要手动创建UI元素和约束
        if titleLabel == nil {
            createUIElements()
        }

        // 设置约束
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        descriptionLabel.translatesAutoresizingMaskIntoConstraints = false
        qualitySegmentedControl.translatesAutoresizingMaskIntoConstraints = false
        startButton.translatesAutoresizingMaskIntoConstraints = false
        cancelButton.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // 标题
            titleLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 40),
            titleLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            titleLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),

            // 描述
            descriptionLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 20),
            descriptionLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            descriptionLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),

            // 质量选择
            qualitySegmentedControl.topAnchor.constraint(equalTo: descriptionLabel.bottomAnchor, constant: 40),
            qualitySegmentedControl.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            qualitySegmentedControl.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            qualitySegmentedControl.heightAnchor.constraint(equalToConstant: 44),

            // 开始按钮
            startButton.topAnchor.constraint(equalTo: qualitySegmentedControl.bottomAnchor, constant: 40),
            startButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            startButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            startButton.heightAnchor.constraint(equalToConstant: 50),

            // 取消按钮
            cancelButton.topAnchor.constraint(equalTo: startButton.bottomAnchor, constant: 20),
            cancelButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            cancelButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            cancelButton.heightAnchor.constraint(equalToConstant: 50)
        ])
    }

    private func createUIElements() {
        titleLabel = UILabel()
        descriptionLabel = UILabel()
        qualitySegmentedControl = UISegmentedControl()
        startButton = UIButton(type: .system)
        cancelButton = UIButton(type: .system)

        view.addSubview(titleLabel)
        view.addSubview(descriptionLabel)
        view.addSubview(qualitySegmentedControl)
        view.addSubview(startButton)
        view.addSubview(cancelButton)
    }

    // MARK: - Actions

    @objc private func qualityChanged() {
        selectedQuality = StreamQuality(rawValue: qualitySegmentedControl.selectedSegmentIndex) ?? .medium
        print("Quality changed to: \(selectedQuality.title)")
    }

    @objc private func startButtonTapped() {
        userDidFinishSetup()
    }

    @objc private func cancelButtonTapped() {
        userDidCancelSetup()
    }

    // MARK: - Setup Methods

    func userDidFinishSetup() {
        // 创建设置信息
        let setupInfo: [String: NSCoding & NSObjectProtocol] = [
            "quality": selectedQuality.rawValue as NSCoding & NSObjectProtocol,
            "bitrate": selectedQuality.bitrate as NSCoding & NSObjectProtocol,
            "appName": "MirrorPlay" as NSCoding & NSObjectProtocol
        ]

        // 创建广播URL（可选）
        let broadcastURL = URL(string: "mirrorplay://broadcast/\(UUID().uuidString)")

        // 完成设置
        extensionContext?.completeRequest(withBroadcast: broadcastURL, setupInfo: setupInfo)
    }

    func userDidCancelSetup() {
        let error = NSError(
            domain: "com.mirrorplay.setup",
            code: -1,
            userInfo: [NSLocalizedDescriptionKey: "用户取消了投屏设置"]
        )

        extensionContext?.cancelRequest(withError: error)
    }
}
