# MirrorPlay 使用说明和代码解析

## 📱 应用概述

MirrorPlay 是一个完全基于苹果原生API的投屏应用，可以将iPhone/iPad的屏幕内容实时传输到另一台设备上显示。

### 🔧 技术架构

```
发送端设备                    接收端设备
┌─────────────────┐          ┌─────────────────┐
│   主应用界面     │          │   播放器界面     │
│ ViewController  │          │PlayerViewController│
└─────────────────┘          └─────────────────┘
         │                            │
         ▼                            ▼
┌─────────────────┐          ┌─────────────────┐
│  ReplayKit扩展  │          │   解码器模块     │
│ SampleHandler   │          │VideoDecoder     │
└─────────────────┘          │AudioDecoder     │
         │                   └─────────────────┘
         ▼                            ▲
┌─────────────────┐                   │
│   编码器模块     │                   │
│ VideoEncoder    │                   │
│ AudioEncoder    │                   │
└─────────────────┘                   │
         │                            │
         ▼                            │
┌─────────────────┐          ┌─────────────────┐
│   网络传输       │ ◄────────┤   网络接收       │
│NetworkManager   │   WiFi   │NetworkManager   │
│   (服务器)       │          │   (客户端)       │
└─────────────────┘          └─────────────────┘
         │                            ▲
         ▼                            │
┌─────────────────┐          ┌─────────────────┐
│   蓝牙控制       │ ◄────────┤   蓝牙连接       │
│BluetoothManager │ 蓝牙通信  │BluetoothManager │
└─────────────────┘          └─────────────────┘
```

## 🚀 工作流程详解

### 1. 发送端启动流程

```swift
// 用户点击"开始投屏"按钮
startStopButtonTapped() 
    ↓
// 检查设备权限和能力
checkPermissions()
    ↓
// 启动网络服务器（监听8888端口）
networkManager.startServer()
    ↓
// 启动蓝牙广播（让其他设备发现）
bluetoothManager.startAdvertising()
    ↓
// 启动屏幕录制
startScreenRecording()
    ↓
// 系统弹出录屏选择界面
// 用户选择"MirrorPlay"扩展
    ↓
// SampleHandler开始接收屏幕数据
processSampleBuffer()
    ↓
// 视频数据 → VideoEncoder → H.264编码
// 音频数据 → AudioEncoder → AAC编码
    ↓
// 编码完成的数据通过网络发送
networkManager.sendVideoFrame()
networkManager.sendAudioFrame()
```

### 2. 接收端连接流程

```swift
// 用户输入发送端IP地址，点击"连接"
connectButtonTapped()
    ↓
// 连接到发送端的TCP服务器
networkManager.connectToServer(host: ipAddress)
    ↓
// 连接成功后启动解码器
videoDecoder.startDecoding()
audioDecoder.startDecoding()
audioPlayer.start()
    ↓
// 开始接收数据包
networkManager.didReceiveVideoFrame()
networkManager.didReceiveAudioFrame()
    ↓
// 解码并播放
videoDecoder.decodeFrame() → 显示到屏幕
audioDecoder.decodeAudioFrame() → 播放音频
```

## 📦 核心模块详解

### 1. NetworkManager（网络管理器）

**作用**：负责WiFi网络的数据传输

**关键概念**：
- **TCP协议**：可靠的数据传输协议，确保数据不丢失
- **服务器模式**：发送端创建服务器，等待连接
- **客户端模式**：接收端连接到服务器
- **数据包格式**：自定义的数据包结构，包含类型、时间戳、大小、数据

```swift
// 数据包结构（总共13字节头部 + 数据）
┌─────────┬─────────────┬─────────────┬──────────┐
│ 类型(1) │ 时间戳(8)   │ 数据大小(4) │ 数据(变长) │
└─────────┴─────────────┴─────────────┴──────────┘
```

### 2. VideoEncoder（视频编码器）

**作用**：将原始视频帧压缩为H.264格式

**关键概念**：
- **CVPixelBuffer**：iOS中的像素缓冲区，包含原始图像数据
- **H.264编码**：高效的视频压缩标准
- **硬件加速**：使用设备的专用芯片进行编码，速度快、功耗低
- **关键帧**：包含完整图像的帧，用于错误恢复
- **码率控制**：控制视频质量和文件大小的平衡

### 3. VideoDecoder（视频解码器）

**作用**：将H.264数据解码为可显示的图像

**关键概念**：
- **Annex-B格式**：H.264的一种封装格式
- **NALU**：网络抽象层单元，H.264的基本数据单位
- **SPS/PPS**：序列参数集/图像参数集，包含解码所需的参数
- **AVSampleBufferDisplayLayer**：iOS提供的高效视频显示组件

### 4. BluetoothManager（蓝牙管理器）

**作用**：通过蓝牙进行设备发现和控制

**关键概念**：
- **BLE**：低功耗蓝牙技术
- **GATT服务**：蓝牙通用属性协议
- **特征值**：蓝牙服务中的数据单元
- **广播模式**：发送端广播服务，接收端扫描发现

## 🎯 使用步骤

### 准备工作

1. **设备要求**：
   - iOS 13.0 或更高版本
   - 支持ReplayKit的设备
   - 两台设备需要在同一WiFi网络下

2. **权限设置**：
   - 蓝牙使用权限
   - 本地网络访问权限
   - 麦克风权限（如需录制音频）

### 发送端操作

1. 打开MirrorPlay应用
2. 确保设备连接到WiFi网络
3. 选择投屏质量（低/中/高）
4. 点击"开始投屏"按钮
5. 在系统录屏界面选择"MirrorPlay"
6. 配置完成后开始录屏
7. 应用显示"正在投屏"状态和本机IP地址

### 接收端操作

1. 在同一台设备上点击"切换到接收端"
   或在另一台设备上打开MirrorPlay
2. 切换到接收端界面
3. 输入发送端显示的IP地址
4. 点击"连接"按钮
5. 连接成功后开始显示投屏内容

### 蓝牙连接（可选）

1. 接收端点击"蓝牙扫描"
2. 自动发现并连接到发送端
3. 可通过蓝牙发送控制命令

## 🔧 代码结构说明

### 主要文件功能

- **ViewController.swift**：发送端主界面，控制投屏开始/停止
- **PlayerViewController.swift**：接收端界面，显示投屏内容
- **SampleHandler.swift**：ReplayKit扩展，捕获屏幕内容
- **NetworkManager.swift**：网络通信管理
- **VideoEncoder.swift**：视频编码处理
- **VideoDecoder.swift**：视频解码处理
- **AudioEncoder.swift**：音频编码处理
- **AudioDecoder.swift**：音频解码处理
- **BluetoothManager.swift**：蓝牙通信管理

### 数据流向

```
屏幕内容 → ReplayKit → 编码器 → 网络传输 → 解码器 → 显示播放
   ↑                                                      ↓
蓝牙控制 ←─────────────── 控制命令 ←─────────────────────────┘
```

## 🐛 常见问题

1. **无法开始录屏**：检查ReplayKit权限和设备支持
2. **网络连接失败**：确认IP地址正确，检查防火墙设置
3. **蓝牙连接问题**：检查蓝牙权限，确认蓝牙已开启
4. **音视频不同步**：检查网络延迟，调整缓冲区设置

## 📈 性能优化

- 使用硬件编解码器提高效率
- 后台线程处理网络和编码操作
- 自适应码率根据网络状况调整质量
- 合理的缓冲区管理减少延迟

这个项目展示了如何使用苹果原生技术栈实现高质量的投屏功能，代码结构清晰，注释详细，适合学习iOS多媒体开发。
