# MirrorPlay 投屏功能测试指南

## 🎯 测试目标

验证 MirrorPlay 投屏应用的完整功能，包括：
- ReplayKit 扩展正确加载
- 主应用与扩展间的通信
- 网络传输功能
- 蓝牙控制功能
- 视频音频编解码

## 📋 测试前准备

### 1. 设备要求
- **发送端**：iPhone/iPad (iOS 13.0+)
- **接收端**：iPhone/iPad 或同一设备的另一个应用实例
- **网络环境**：两台设备连接到同一WiFi网络

### 2. 权限确认
确保应用已获得以下权限：
- ✅ 蓝牙使用权限
- ✅ 本地网络访问权限  
- ✅ 麦克风访问权限
- ✅ 屏幕录制权限

### 3. 编译安装
```bash
# 清理项目
xcodebuild clean -project MirrorPlay.xcodeproj

# 编译安装到真机
xcodebuild -project MirrorPlay.xcodeproj -scheme MirrorPlay -destination "platform=iOS,name=你的设备名称"
```

## 🧪 测试步骤

### 阶段1：扩展加载测试

1. **启动主应用**
   ```
   预期日志：
   🔧 网络管理器初始化完成
   📡 蓝牙管理器初始化完成
   ✅ 功能模块初始化完成
   ```

2. **点击"开始投屏"按钮**
   ```
   预期日志：
   🚀 开始投屏流程...
   ✅ 权限检查通过
   🔌 尝试使用端口: 8889 (或其他可用端口)
   ✅ 网络服务器启动成功
   📡 蓝牙广播已启动
   📱 准备启动屏幕录制...
   ✅ 找到广播扩展，显示选择界面
   ```

3. **在系统录屏界面选择"MirrorPlay"**
   ```
   预期结果：
   - 看到 MirrorPlay 选项
   - 可以点击进入设置界面
   ```

### 阶段2：扩展通信测试

4. **扩展启动时的日志**
   ```
   预期日志：
   🔧 扩展Bundle ID: com.test.MirrorPlay.play.upload
   📁 扩展Bundle路径: /path/to/upload.appex
   🌐 设置扩展网络管理器...
   📖 读取服务器IP: *************
   📖 读取服务器端口: 8889
   📡 准备连接到主应用服务器: *************:8889
   ✅ 扩展成功连接到主应用服务器
   💾 保存推流状态: true
   ```

5. **开始录屏后的数据传输**
   ```
   预期日志：
   📹 创建视频编码器: 1920x1080, 2000000bps, 30fps
   🚀 启动视频编码器...
   ✅ 视频编码器启动成功
   🎵 音频编码器启动成功: 44100.0 Hz, 2 channels
   📱 开始处理视频帧...
   📊 开始处理音频帧...
   ```

### 阶段3：接收端测试

6. **启动接收端**
   - 点击主应用中的"切换到接收端"按钮
   - 或在另一台设备上启动应用并切换到接收端

7. **连接到发送端**
   ```
   操作：
   1. 输入发送端显示的IP地址
   2. 点击"连接"按钮
   
   预期日志：
   🔌 用户点击连接
   🌐 连接到发送端服务器...
   ✅ 连接成功
   🎬 设置视频显示组件...
   🔧 初始化接收端功能模块...
   ```

8. **验证视频播放**
   ```
   预期结果：
   - 接收端显示发送端的屏幕内容
   - 画面流畅，延迟较低
   - 音频同步播放
   - 显示帧率统计信息
   ```

### 阶段4：蓝牙控制测试

9. **蓝牙连接**
   ```
   操作：
   1. 接收端点击"蓝牙扫描"
   2. 等待自动发现发送端
   
   预期日志：
   📡 开始蓝牙扫描
   🔍 发现设备: MirrorPlay
   ✅ 蓝牙连接成功
   ```

10. **远程控制**
    ```
    测试场景：
    - 通过蓝牙发送停止命令
    - 通过蓝牙发送暂停/恢复命令
    - 验证命令正确执行
    ```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 扩展不显示
```
症状：系统录屏界面没有MirrorPlay选项
检查：
- 扩展是否正确嵌入：ls -la App.app/PlugIns/
- Bundle ID层次结构是否正确
- Info.plist配置是否完整

解决：
- 重新编译项目
- 检查项目配置中的Embed Foundation Extensions设置
```

#### 2. 网络连接失败
```
症状：扩展无法连接到主应用服务器
检查：
- 主应用是否成功启动服务器
- 共享存储中的服务器信息是否正确
- 网络权限是否已授予

解决：
- 检查防火墙设置
- 确认设备在同一网络
- 重启应用重新获取IP地址
```

#### 3. 视频不显示
```
症状：接收端连接成功但无画面
检查：
- 视频编码器是否正常启动
- 网络数据传输是否正常
- 解码器是否正确初始化

解决：
- 检查编码参数设置
- 验证数据包格式
- 重启录屏功能
```

#### 4. 音频不同步
```
症状：画面正常但音频延迟或无声
检查：
- 音频编码器状态
- 麦克风权限
- 音频播放器初始化

解决：
- 调整音频缓冲区大小
- 检查音频格式兼容性
- 重新授权麦克风权限
```

## 📊 性能指标

### 正常运行指标
- **延迟**：< 200ms
- **帧率**：25-30 FPS
- **码率**：1-4 Mbps（根据质量设置）
- **CPU使用率**：< 50%
- **内存使用**：< 100MB

### 监控方法
```swift
// 在代码中添加性能监控
let startTime = CACurrentMediaTime()
// ... 执行操作
let endTime = CACurrentMediaTime()
print("操作耗时: \((endTime - startTime) * 1000)ms")
```

## 📝 测试报告模板

```
测试日期：____
测试设备：____
iOS版本：____

✅ 扩展加载：通过/失败
✅ 网络连接：通过/失败  
✅ 视频传输：通过/失败
✅ 音频传输：通过/失败
✅ 蓝牙控制：通过/失败

问题记录：
1. ____
2. ____

性能数据：
- 平均延迟：____ms
- 平均帧率：____fps
- 内存使用：____MB
```

## 🎉 成功标准

测试通过的标准：
- ✅ 扩展在系统界面正确显示
- ✅ 主应用与扩展成功通信
- ✅ 视频音频正常传输和播放
- ✅ 蓝牙控制功能正常
- ✅ 性能指标在合理范围内
- ✅ 无严重内存泄漏或崩溃

完成所有测试后，您的 MirrorPlay 投屏应用就可以正常使用了！
