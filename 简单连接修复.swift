// 在 PlayerViewController.swift 中的 connect() 方法里添加这段代码

private func connect() {
    guard let ipAddress = ipTextField.text, !ipAddress.isEmpty else {
        showAlert(title: "错误", message: "请输入服务器IP地址")
        return
    }
    
    statusLabel.text = "连接中..."
    connectButton.isEnabled = false
    
    // 连接到服务器
    networkManager?.connectToServer(host: ipAddress) { [weak self] result in
        DispatchQueue.main.async {
            switch result {
            case .success:
                print("✅ 网络连接成功")
                
                // 立即发送握手消息保持连接
                let handshakeData = "CLIENT_READY".data(using: .utf8) ?? Data()
                self?.networkManager?.sendControlCommand(handshakeData)
                print("📤 发送握手消息保持连接")
                
                self?.onConnected()
            case .failure(let error):
                print("❌ 连接失败: \(error)")
                self?.onConnectionFailed(error)
            }
        }
    }
}

// 修改 onConnected 方法，移除重复的握手消息
private func onConnected() {
    isConnected = true
    print("🎯 连接成功，开始初始化解码器...")
    
    // 启动解码器
    do {
        print("📹 启动视频解码器...")
        try videoDecoder?.startDecoding()
        print("🎵 启动音频解码器...")
        try audioDecoder?.startDecoding()
        print("🔊 启动音频播放器...")
        try audioPlayer?.start()
        print("✅ 所有解码器启动成功")
    } catch {
        print("❌ 启动解码器失败: \(error)")
        showAlert(title: "错误", message: "启动解码器失败: \(error.localizedDescription)")
        disconnect()
        return
    }
    
    updateUI()
}
