# MirrorPlay 调试指南

## 问题描述
连接成功但没有画面显示

## 可能的原因和解决方案

### 1. 网络连接问题

**症状**: 连接成功但收不到数据
**检查步骤**:
1. 确认两台设备在同一WiFi网络下
2. 检查防火墙设置，确保8888端口未被阻止
3. 使用网络测试脚本验证连接

**解决方案**:
```bash
# 使用提供的测试脚本
python3 test_connection.py 192.168.1.100
```

### 2. 视频编码/解码问题

**症状**: 收到数据但无法解码
**检查步骤**:
1. 查看控制台日志，寻找以下关键词：
   - "📹 接收到SPS/PPS"
   - "✅ 格式描述符创建成功"
   - "✅ 解码会话创建成功"
   - "🎬 解码成功"

**常见问题**:
- SPS/PPS数据未正确接收
- 解码会话创建失败
- 视频格式不兼容

### 3. 视频显示问题

**症状**: 解码成功但画面不显示
**检查步骤**:
1. 确认AVSampleBufferDisplayLayer正确创建
2. 检查视频显示层的frame设置
3. 验证CMSampleBuffer创建是否成功

### 4. 权限问题

**症状**: 应用崩溃或功能异常
**检查步骤**:
1. 确认Info.plist包含所有必要权限
2. 检查系统设置中的权限授权
3. 重新安装应用并重新授权

## 调试步骤

### 步骤1: 检查网络连接
1. 在发送端启动投屏
2. 查看控制台输出，确认网络服务器启动成功
3. 在接收端连接，查看连接状态

### 步骤2: 检查数据流
1. 查看控制台日志中的数据传输信息
2. 确认视频帧数据正在传输
3. 检查数据包大小是否合理

### 步骤3: 检查解码过程
1. 查看SPS/PPS接收日志
2. 确认格式描述符创建成功
3. 验证解码会话创建成功

### 步骤4: 检查显示过程
1. 确认解码回调被调用
2. 检查CMSampleBuffer创建
3. 验证显示层状态

## 控制台日志解读

### 正常流程日志
```
🌐 网络服务器启动成功
✅ 服务器客户端连接: client-xxx
📦 接收到数据包头部，数据大小: 1234 字节
✅ 接收到完整数据包，总大小: 1247 字节
📹 接收到视频帧: 1234 字节, 时间戳: 1234567890
📹 接收到SPS: 45 字节
📹 接收到PPS: 12 字节
✅ 格式描述符创建成功
✅ 解码会话创建成功
🎬 解码成功，时间戳: 1234567890
🎬 视频解码成功，准备显示帧，时间戳: 1234567890
```

### 问题日志
```
❌ 网络服务器启动失败
❌ 数据包不完整，期望1234字节，实际1000字节
❌ 格式描述符创建失败: -12345
❌ 解码会话创建失败: -12345
⚠️ 解码回调中imageBuffer为空
```

## 常见错误代码

| 错误代码 | 含义 | 解决方案 |
|---------|------|----------|
| -12345 | 格式描述符创建失败 | 检查SPS/PPS数据完整性 |
| -12346 | 解码会话创建失败 | 确认硬件解码器可用 |
| -12347 | 数据包解析失败 | 检查网络传输稳定性 |

## 性能优化建议

1. **降低视频质量**: 在发送端选择"低质量"模式
2. **减少帧率**: 修改VideoEncoder中的fps设置
3. **增加缓冲区**: 调整网络传输缓冲区大小
4. **优化网络**: 使用有线连接或5GHz WiFi

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 设备型号和iOS版本
2. 完整的控制台日志
3. 网络环境描述
4. 问题复现步骤 