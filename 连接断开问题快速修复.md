# 连接断开问题快速修复方案

## 🚨 问题现状

客户端连接到服务器后立即断开，日志显示：
```
Connected to server
✅ 网络连接成功
❌ 服务器客户端断开: client-xxx
```

## 🔍 问题分析

这是一个典型的**TCP连接保持问题**：

1. **客户端成功连接**到服务器
2. **服务器接受连接**但立即开始等待数据
3. **客户端没有立即发送数据**
4. **服务器认为连接无效**，断开连接

## 🛠️ 快速修复方案

### 方案1：客户端连接后立即发送握手消息

在 `PlayerViewController` 的连接成功回调中添加：

```swift
// 连接成功后立即发送握手消息
private func sendHandshakeMessage() {
    let handshakeData = "CLIENT_READY".data(using: .utf8) ?? Data()
    networkManager?.sendControlCommand(handshakeData)
    print("📤 已发送握手消息")
}

// 在连接成功后调用
func onConnected() {
    isConnected = true
    
    // 发送握手消息保持连接
    sendHandshakeMessage()
    
    // 启动解码器...
}
```

### 方案2：修改服务器接收逻辑

在 `NetworkManager.swift` 中修改 `startReceiving` 方法：

```swift
private func startReceiving(connection: NWConnection, clientId: String) {
    // 不要立即要求数据，而是保持连接监听
    connection.receive(minimumIncompleteLength: 0, maximumLength: 65536) { [weak self] data, _, isComplete, error in
        if let error = error {
            print("❌ 连接错误: \(error)")
            return
        }
        
        if isComplete {
            print("📱 客户端正常断开")
            self?.connections.removeValue(forKey: clientId)
            self?.delegate?.networkManager(self!, clientDidDisconnect: clientId)
            return
        }
        
        // 如果有数据就处理，没有数据就继续等待
        if let receivedData = data, !receivedData.isEmpty {
            print("📦 收到数据: \(receivedData.count) 字节")
            // 处理数据...
        }
        
        // 继续监听
        self?.startReceiving(connection: connection, clientId: clientId)
    }
}
```

### 方案3：添加连接保活机制

```swift
// 在客户端定期发送心跳
private func startHeartbeat() {
    Timer.scheduledTimer(withTimeInterval: 10.0, repeats: true) { [weak self] _ in
        let heartbeatData = "HEARTBEAT".data(using: .utf8) ?? Data()
        self?.networkManager?.sendControlCommand(heartbeatData)
    }
}
```

## 🧪 测试步骤

1. **实施方案1**：客户端连接后立即发送握手消息
2. **测试连接**：看是否还会立即断开
3. **如果仍然断开**：实施方案2修改服务器逻辑
4. **最后添加方案3**：心跳保活机制

## 📝 最简单的修复

**立即尝试这个修复**：

在 `PlayerViewController.swift` 的 `onConnected()` 方法中添加：

```swift
private func onConnected() {
    isConnected = true
    
    // 立即发送一个测试消息保持连接
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
        let testData = "CLIENT_CONNECTED".data(using: .utf8) ?? Data()
        self.networkManager?.sendControlCommand(testData)
        print("📤 发送连接确认消息")
    }
    
    // 启动解码器...
    do {
        try videoDecoder?.startDecoding()
        try audioDecoder?.startDecoding()
        try audioPlayer?.start()
    } catch {
        // 错误处理...
    }
    
    updateUI()
}
```

这样客户端连接后会立即发送一个消息，告诉服务器"我已经准备好了"，避免服务器因为没有数据而断开连接。

## 🎯 预期结果

修复后应该看到：
```
Connected to server
✅ 网络连接成功
📤 发送连接确认消息
✅ 连接保持稳定
```

而不是立即断开。
